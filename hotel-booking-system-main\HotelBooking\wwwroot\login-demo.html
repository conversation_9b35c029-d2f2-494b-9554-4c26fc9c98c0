<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sign In - HotelPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Login Page Styles */
        .login-body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 20px;
        }

        .login-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            padding: 40px 30px;
            text-align: center;
        }

        /* Header Section */
        .login-header {
            margin-bottom: 30px;
        }

        .logo-container {
            margin-bottom: 15px;
        }

        .logo-icon {
            font-size: 48px;
            color: #007bff;
            background: linear-gradient(135deg, #007bff, #0056b3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin: 10px 0 5px 0;
        }

        .brand-subtitle {
            color: #6c757d;
            font-size: 14px;
            margin: 0;
        }

        /* Welcome Section */
        .welcome-section {
            margin-bottom: 25px;
        }

        .welcome-title {
            font-size: 24px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .welcome-subtitle {
            color: #6c757d;
            font-size: 14px;
            margin: 0;
        }

        /* Tab Navigation */
        .tab-navigation {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 25px;
        }

        .tab-btn {
            flex: 1;
            padding: 10px 20px;
            border: none;
            background: transparent;
            color: #6c757d;
            font-weight: 500;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: white;
            color: #2c3e50;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tab-btn:hover:not(.active) {
            color: #495057;
        }

        /* Form Styles */
        .login-form {
            text-align: left;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .form-control::placeholder {
            color: #adb5bd;
        }

        /* Sign In Button */
        .btn-signin {
            width: 100%;
            padding: 14px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            margin-top: 10px;
        }

        .btn-signin:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-signin:active {
            transform: translateY(0);
        }

        /* Demo Section */
        .demo-section {
            margin-top: 30px;
            padding-top: 25px;
            border-top: 1px solid #e9ecef;
        }

        .demo-title {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .demo-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .demo-btn {
            padding: 8px 16px;
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .demo-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-1px);
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .login-container {
                padding: 15px;
            }
            
            .login-card {
                padding: 30px 20px;
            }
            
            .demo-buttons {
                flex-direction: column;
                gap: 8px;
            }
            
            .demo-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body class="login-body">
    <div class="login-container">
        <div class="login-card">
            <!-- Logo and Title -->
            <div class="login-header">
                <div class="logo-container">
                    <i class="fas fa-building logo-icon"></i>
                </div>
                <h1 class="brand-title">HotelPro</h1>
                <p class="brand-subtitle">Hotel Management System</p>
            </div>

            <!-- Welcome Section -->
            <div class="welcome-section">
                <h2 class="welcome-title">Welcome</h2>
                <p class="welcome-subtitle">Sign in to your account or create a new one</p>
            </div>

            <!-- Tab Navigation -->
            <div class="tab-navigation">
                <button class="tab-btn active" id="signin-tab">Sign In</button>
                <button class="tab-btn" id="signup-tab">Sign Up</button>
            </div>

            <!-- Login Form -->
            <form class="login-form" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-control" placeholder="Enter your email" id="email" required />
                </div>

                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" placeholder="Enter your password" id="password" required />
                </div>

                <button type="submit" class="btn-signin">Sign In</button>
            </form>

            <!-- Demo Accounts -->
            <div class="demo-section">
                <p class="demo-title">Demo Accounts:</p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="fillDemoAccount('admin')">Admin</button>
                    <button class="demo-btn" onclick="fillDemoAccount('manager')">Manager</button>
                    <button class="demo-btn" onclick="fillDemoAccount('guest')">Guest</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Tab functionality
        document.getElementById('signin-tab').addEventListener('click', function() {
            this.classList.add('active');
            document.getElementById('signup-tab').classList.remove('active');
        });
        
        document.getElementById('signup-tab').addEventListener('click', function() {
            this.classList.add('active');
            document.getElementById('signin-tab').classList.remove('active');
            alert('Sign Up functionality will be implemented soon!');
            setTimeout(() => {
                document.getElementById('signin-tab').classList.add('active');
                this.classList.remove('active');
            }, 100);
        });

        // Demo account functionality
        function fillDemoAccount(role) {
            const accounts = {
                'admin': { email: '<EMAIL>', password: 'Admin123!' },
                'manager': { email: '<EMAIL>', password: 'Manager123!' },
                'guest': { email: '<EMAIL>', password: 'Guest123!' }
            };
            
            if (accounts[role]) {
                document.getElementById('email').value = accounts[role].email;
                document.getElementById('password').value = accounts[role].password;
            }
        }

        // Handle login
        function handleLogin(event) {
            event.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            // Simple demo validation
            const validAccounts = {
                '<EMAIL>': 'Admin123!',
                '<EMAIL>': 'Manager123!',
                '<EMAIL>': 'Guest123!'
            };
            
            if (validAccounts[email] && validAccounts[email] === password) {
                alert(`Login successful! Welcome ${email.split('@')[0]}.`);
                // Here you would redirect to the dashboard
            } else {
                alert('Invalid email or password. Please try the demo accounts.');
            }
        }

        // Add animation on load
        window.addEventListener('load', function() {
            const loginCard = document.querySelector('.login-card');
            loginCard.style.opacity = '0';
            loginCard.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                loginCard.style.transition = 'all 0.5s ease';
                loginCard.style.opacity = '1';
                loginCard.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
