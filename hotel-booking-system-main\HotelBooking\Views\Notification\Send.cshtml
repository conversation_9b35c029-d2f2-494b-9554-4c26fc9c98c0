@model HotelBooking.Models.ViewModels.SendNotificationViewModel
@{
    ViewData["Title"] = "Send Notification";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Send Notification</h3>
                    <div class="card-tools">
                        <a href="@Url.Action("Index")" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        <a href="@Url.Action("Templates")" class="btn btn-info">
                            <i class="fas fa-file-alt"></i> Templates
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form asp-action="Send" method="post">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label asp-for="Title" class="control-label"></label>
                                    <input asp-for="Title" class="form-control" placeholder="Enter notification title..." />
                                    <span asp-validation-for="Title" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="Message" class="control-label"></label>
                                    <textarea asp-for="Message" class="form-control" rows="6" placeholder="Enter notification message..."></textarea>
                                    <span asp-validation-for="Message" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="Type" class="control-label"></label>
                                    <select asp-for="Type" class="form-control">
                                        <option value="">Select Notification Type</option>
                                        <option value="Email">Email</option>
                                        <option value="SMS">SMS</option>
                                        <option value="System">System Notification</option>
                                    </select>
                                    <span asp-validation-for="Type" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h5>Recipient Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label asp-for="SendTo" class="control-label"></label>
                                            <select asp-for="SendTo" class="form-control" id="sendToSelect">
                                                <option value="">Select Recipients</option>
                                                <option value="All">All Users</option>
                                                <option value="Specific">Specific Users</option>
                                                <option value="Role">By Role</option>
                                            </select>
                                            <span asp-validation-for="SendTo" class="text-danger"></span>
                                        </div>

                                        <div class="form-group" id="specificUsersDiv" style="display: none;">
                                            <label asp-for="UserIDs" class="control-label">Select Users</label>
                                            <select asp-for="UserIDs" class="form-control" multiple>
                                                @if (Model.Users != null)
                                                {
                                                    @foreach (var user in Model.Users)
                                                    {
                                                        <option value="@user.Id">@user.UserName (@user.Email)</option>
                                                    }
                                                }
                                            </select>
                                            <small class="form-text text-muted">Hold Ctrl to select multiple users</small>
                                        </div>

                                        <div class="form-group" id="roleDiv" style="display: none;">
                                            <label asp-for="RoleID" class="control-label">Select Role</label>
                                            <select asp-for="RoleID" class="form-control">
                                                <option value="">Select Role</option>
                                                @if (Model.Roles != null)
                                                {
                                                    @foreach (var role in Model.Roles)
                                                    {
                                                        <option value="@role.Id">@role.Name</option>
                                                    }
                                                }
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="card bg-light mt-3">
                                    <div class="card-header">
                                        <h5>Scheduling</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input asp-for="SendImmediately" class="form-check-input" type="checkbox" id="sendImmediatelyCheck" />
                                            <label class="form-check-label" for="sendImmediatelyCheck">
                                                Send Immediately
                                            </label>
                                        </div>

                                        <div class="form-group mt-2" id="scheduleDiv" style="display: none;">
                                            <label asp-for="ScheduleDate" class="control-label">Schedule Date</label>
                                            <input asp-for="ScheduleDate" class="form-control" type="datetime-local" />
                                            <span asp-validation-for="ScheduleDate" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-3">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-paper-plane"></i> Send Notification
                            </button>
                            <a href="@Url.Action("Index")" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Handle send to selection change
            $('#sendToSelect').change(function() {
                var sendTo = $(this).val();
                
                $('#specificUsersDiv').hide();
                $('#roleDiv').hide();
                
                if (sendTo === 'Specific') {
                    $('#specificUsersDiv').show();
                } else if (sendTo === 'Role') {
                    $('#roleDiv').show();
                }
            });

            // Handle send immediately checkbox
            $('#sendImmediatelyCheck').change(function() {
                if ($(this).is(':checked')) {
                    $('#scheduleDiv').hide();
                } else {
                    $('#scheduleDiv').show();
                }
            });

            // Initialize based on current values
            $('#sendToSelect').trigger('change');
            $('#sendImmediatelyCheck').trigger('change');
        });
    </script>
}
