@model HotelBooking.Models.ViewModels.GuestListViewModel
@{
    ViewData["Title"] = "Guest Profile Management";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Guest Profile Management</h3>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" name="searchTerm" value="@Model.SearchTerm" class="form-control" placeholder="Search guests..." />
                            </div>
                            <div class="col-md-2">
                                <select name="sortBy" class="form-control">
                                    <option value="">Sort By</option>
                                    <option value="name" selected="@(Model.SortBy == "name")">Name</option>
                                    <option value="email" selected="@(Model.SortBy == "email")">Email</option>
                                    <option value="country" selected="@(Model.SortBy == "country")">Country</option>
                                    <option value="created" selected="@(Model.SortBy == "created")">Created Date</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="sortOrder" class="form-control">
                                    <option value="asc" selected="@(Model.SortOrder == "asc")">Ascending</option>
                                    <option value="desc" selected="@(Model.SortOrder == "desc")">Descending</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary">Search</button>
                            </div>
                            <div class="col-md-2">
                                <a href="@Url.Action("Index")" class="btn btn-secondary">Clear</a>
                            </div>
                        </div>
                    </form>

                    <!-- Statistics -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <strong>Total Guests:</strong> @Model.TotalGuests
                            </div>
                        </div>
                    </div>

                    <!-- Guest List Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Country</th>
                                    <th>State</th>
                                    <th>Age Group</th>
                                    <th>Created Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Guests != null && Model.Guests.Any())
                                {
                                    @foreach (var guest in Model.Guests)
                                    {
                                        <tr>
                                            <td>@guest.FirstName @guest.LastName</td>
                                            <td>@guest.Email</td>
                                            <td>@guest.Phone</td>
                                            <td>@guest.CountryName</td>
                                            <td>@guest.StateName</td>
                                            <td>@guest.AgeGroup</td>
                                            <td>@guest.CreatedDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("Details", new { id = guest.GuestID })" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <a href="@Url.Action("Edit", new { id = guest.GuestID })" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                    <a href="@Url.Action("Delete", new { id = guest.GuestID })" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="8" class="text-center">No guests found.</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if (Model.TotalPages > 1)
                    {
                        <nav aria-label="Guest pagination">
                            <ul class="pagination justify-content-center">
                                @if (Model.CurrentPage > 1)
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage - 1, searchTerm = Model.SearchTerm, sortBy = Model.SortBy, sortOrder = Model.SortOrder })">Previous</a>
                                    </li>
                                }

                                @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                {
                                    <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                        <a class="page-link" href="@Url.Action("Index", new { page = i, searchTerm = Model.SearchTerm, sortBy = Model.SortBy, sortOrder = Model.SortOrder })">@i</a>
                                    </li>
                                }

                                @if (Model.CurrentPage < Model.TotalPages)
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage + 1, searchTerm = Model.SearchTerm, sortBy = Model.SortBy, sortOrder = Model.SortOrder })">Next</a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Add any JavaScript for guest management here
        $(document).ready(function() {
            // Initialize tooltips
            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>
}
