@model HotelBooking.Models.ViewModels.GuestProfileViewModel
@{
    ViewData["Title"] = "Delete Guest Profile";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle"></i> Delete Guest Profile
                    </h3>
                </div>

                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> Warning!</h5>
                        Are you sure you want to delete this guest profile? This action cannot be undone.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Name:</dt>
                                <dd class="col-sm-8">
                                    <strong>@Model.FirstName @Model.LastName</strong>
                                </dd>

                                <dt class="col-sm-4">Email:</dt>
                                <dd class="col-sm-8">@Model.Email</dd>

                                <dt class="col-sm-4">Phone:</dt>
                                <dd class="col-sm-8">@(Model.Phone ?? "Not provided")</dd>

                                <dt class="col-sm-4">Username:</dt>
                                <dd class="col-sm-8">@Model.UserName</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Country:</dt>
                                <dd class="col-sm-8">@(Model.CountryName ?? "Not specified")</dd>

                                <dt class="col-sm-4">State:</dt>
                                <dd class="col-sm-8">@(Model.StateName ?? "Not specified")</dd>

                                <dt class="col-sm-4">Created Date:</dt>
                                <dd class="col-sm-8">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</dd>

                                <dt class="col-sm-4">Guest ID:</dt>
                                <dd class="col-sm-8">#@Model.GuestID</dd>
                            </dl>
                        </div>
                    </div>

                    <hr />
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Impact Assessment</h6>
                        <p><strong>Before deleting this guest profile, please note:</strong></p>
                        <ul class="mb-0">
                            <li>All reservation history for this guest will be affected</li>
                            <li>Any pending reservations may need to be handled separately</li>
                            <li>Feedback and reviews from this guest will remain in the system</li>
                            <li>This action cannot be undone</li>
                        </ul>
                    </div>
                </div>

                <div class="card-footer">
                    <form asp-action="Delete" method="post" class="d-inline">
                        <input type="hidden" asp-for="GuestID" />
                        <button type="submit" class="btn btn-danger" 
                                onclick="return confirm('Are you absolutely sure you want to delete this guest profile? This action cannot be undone.')">
                            <i class="fas fa-trash"></i> Yes, Delete Guest Profile
                        </button>
                    </form>
                    <a href="@Url.Action("Details", new { id = Model.GuestID })" class="btn btn-info">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Cancel & Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
