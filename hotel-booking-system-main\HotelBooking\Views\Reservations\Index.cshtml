@model IEnumerable<HotelBooking.Models.Reservation>
@{
    ViewData["Title"] = "My Bookings";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="display-6 fw-bold text-primary">
                    <i class="fas fa-calendar-check me-3"></i>My Bookings
                </h1>
                <a asp-controller="Rooms" asp-action="Index" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Book New Room
                </a>
            </div>

            @if (Model.Any())
            {
                <div class="row">
                    @foreach (var reservation in Model)
                    {
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 shadow-sm border-0">
                                <div class="card-header bg-white border-0 pb-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="card-title mb-0 fw-bold">Room @reservation.Room?.RoomNumber</h5>
                                        <span class="badge @(reservation.Status == "Confirmed" ? "bg-success" : 
                                                            reservation.Status == "Pending" ? "bg-warning" : 
                                                            reservation.Status == "Cancelled" ? "bg-danger" : "bg-secondary") fs-6">
                                            @reservation.Status
                                        </span>
                                    </div>
                                    <p class="text-muted mb-0">@reservation.Room?.RoomType?.TypeName</p>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">Check-in</small>
                                            <div class="fw-semibold">@reservation.CheckInDate.ToString("MMM dd, yyyy")</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Check-out</small>
                                            <div class="fw-semibold">@reservation.CheckOutDate.ToString("MMM dd, yyyy")</div>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">Guests</small>
                                            <div class="fw-semibold">@reservation.NumberOfGuests</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Booked on</small>
                                            <div class="fw-semibold">@reservation.BookingDate.ToString("MMM dd, yyyy")</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer bg-white border-0 pt-0">
                                    <div class="d-flex gap-2">
                                        <a asp-action="Details" asp-route-id="@reservation.ReservationID" class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="fas fa-eye me-1"></i>Details
                                        </a>
                                        @if (reservation.Status != "Cancelled" && reservation.CheckInDate > DateTime.Now)
                                        {
                                            <form asp-action="Cancel" asp-route-id="@reservation.ReservationID" method="post" class="flex-fill">
                                                <button type="submit" class="btn btn-outline-danger btn-sm w-100" 
                                                        onclick="return confirm('Are you sure you want to cancel this reservation?')">
                                                    <i class="fas fa-times me-1"></i>Cancel
                                                </button>
                                            </form>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-calendar-times text-muted" style="font-size: 4rem;"></i>
                    </div>
                    <h3 class="text-muted mb-3">No Bookings Yet</h3>
                    <p class="text-muted mb-4">You haven't made any reservations yet. Start exploring our rooms!</p>
                    <a asp-controller="Rooms" asp-action="Index" class="btn btn-primary btn-lg">
                        <i class="fas fa-search me-2"></i>Browse Rooms
                    </a>
                </div>
            }
        </div>
    </div>
</div>
