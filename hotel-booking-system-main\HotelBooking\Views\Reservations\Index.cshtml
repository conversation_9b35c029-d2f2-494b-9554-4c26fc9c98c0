@model IEnumerable<HotelBooking.Models.Reservation>
@{
    ViewData["Title"] = "My Bookings";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="display-6 fw-bold text-primary">
                    <i class="fas fa-calendar-check me-3"></i>My Bookings
                </h1>
                <a asp-controller="Rooms" asp-action="Index" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Book New Room
                </a>
            </div>

            @if (Model.Any())
            {
                <div class="row">
                    @foreach (var reservation in Model)
                    {
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 shadow-sm border-0">
                                <div class="card-header bg-white border-0 pb-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="card-title mb-0 fw-bold">Room @reservation.Room?.RoomNumber</h5>
                                        <span class="badge @(reservation.Status == "Confirmed" ? "bg-success" : 
                                                            reservation.Status == "Pending" ? "bg-warning" : 
                                                            reservation.Status == "Cancelled" ? "bg-danger" : "bg-secondary") fs-6">
                                            @reservation.Status
                                        </span>
                                    </div>
                                    <p class="text-muted mb-0">@reservation.Room?.RoomType?.TypeName</p>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">Check-in</small>
                                            <div class="fw-semibold">@reservation.CheckInDate.ToString("MMM dd, yyyy")</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Check-out</small>
                                            <div class="fw-semibold">@reservation.CheckOutDate.ToString("MMM dd, yyyy")</div>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">Guests</small>
                                            <div class="fw-semibold">@reservation.NumberOfGuests</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Booked on</small>
                                            <div class="fw-semibold">@reservation.BookingDate.ToString("MMM dd, yyyy")</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer bg-white border-0 pt-0">
                                    <div class="d-flex gap-1 mb-2">
                                        <a asp-action="Details" asp-route-id="@reservation.ReservationID" class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="fas fa-eye me-1"></i>Details
                                        </a>
                                        @if (reservation.Status == "Confirmed")
                                        {
                                            @if (reservation.CheckInDate <= DateTime.Today && reservation.CheckOutDate > DateTime.Today)
                                            {
                                                <!-- Currently staying - show in-stay options -->
                                                <a asp-action="ChangeRoom" asp-route-id="@reservation.ReservationID" class="btn btn-outline-warning btn-sm flex-fill">
                                                    <i class="fas fa-exchange-alt me-1"></i>Change Room
                                                </a>
                                            }
                                            @if (reservation.CheckOutDate > DateTime.Today)
                                            {
                                                <!-- Future or current stay - show add service -->
                                                <a asp-action="AddService" asp-route-id="@reservation.ReservationID" class="btn btn-outline-success btn-sm flex-fill">
                                                    <i class="fas fa-plus-circle me-1"></i>Add Service
                                                </a>
                                            }
                                        }
                                    </div>
                                    <div class="d-flex gap-1">
                                        @if (reservation.Status == "Confirmed")
                                        {
                                            @if (reservation.CheckOutDate <= DateTime.Today.AddDays(1))
                                            {
                                                <!-- Ready for checkout -->
                                                <a asp-action="Checkout" asp-route-id="@reservation.ReservationID" class="btn btn-primary btn-sm flex-fill">
                                                    <i class="fas fa-receipt me-1"></i>Checkout
                                                </a>
                                            }
                                            else if (reservation.CheckInDate > DateTime.Today.AddDays(1))
                                            {
                                                <!-- Future reservation - allow cancel -->
                                                <a asp-action="Cancel" asp-route-id="@reservation.ReservationID" class="btn btn-outline-danger btn-sm flex-fill">
                                                    <i class="fas fa-times me-1"></i>Cancel
                                                </a>
                                            }
                                        }
                                        @if (reservation.Status != "Cancelled" && reservation.CheckInDate > DateTime.Today.AddDays(2))
                                        {
                                            <!-- Allow modification if more than 2 days before check-in -->
                                            <a asp-action="Modify" asp-route-id="@reservation.ReservationID" class="btn btn-outline-info btn-sm flex-fill">
                                                <i class="fas fa-edit me-1"></i>Modify
                                            </a>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-calendar-times text-muted" style="font-size: 4rem;"></i>
                    </div>
                    <h3 class="text-muted mb-3">No Bookings Yet</h3>
                    <p class="text-muted mb-4">You haven't made any reservations yet. Start exploring our rooms!</p>
                    <a asp-controller="Rooms" asp-action="Index" class="btn btn-primary btn-lg">
                        <i class="fas fa-search me-2"></i>Browse Rooms
                    </a>
                </div>
            }
        </div>
    </div>
</div>
