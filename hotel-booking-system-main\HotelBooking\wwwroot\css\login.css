/* Login Page Styles */
.login-body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 40px 30px;
    text-align: center;
}

/* Header Section */
.login-header {
    margin-bottom: 30px;
}

.logo-container {
    margin-bottom: 15px;
}

.logo-icon {
    font-size: 48px;
    color: #007bff;
    background: linear-gradient(135deg, #007bff, #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin: 10px 0 5px 0;
}

.brand-subtitle {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
}

/* Welcome Section */
.welcome-section {
    margin-bottom: 25px;
}

.welcome-title {
    font-size: 24px;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 8px;
}

.welcome-subtitle {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 25px;
}

.tab-btn {
    flex: 1;
    padding: 10px 20px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-weight: 500;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: white;
    color: #2c3e50;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
    color: #495057;
}

/* Make link look like button */
a.tab-btn {
    text-decoration: none;
    display: inline-block;
}

/* Form Styles */
.login-form {
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-control::placeholder {
    color: #adb5bd;
}

/* Sign In Button */
.btn-signin {
    width: 100%;
    padding: 14px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    margin-top: 10px;
}

.btn-signin:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-signin:active {
    transform: translateY(0);
}

/* Demo Section */
.demo-section {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid #e9ecef;
}

.demo-title {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 15px;
    font-weight: 500;
}

.demo-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.demo-btn {
    padding: 8px 16px;
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.demo-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

/* Validation Styles */
.text-danger {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.validation-summary-errors {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 14px;
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.field-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: 15px;
    }

    .login-card {
        padding: 30px 20px;
    }

    .demo-buttons {
        flex-direction: column;
        gap: 8px;
    }

    .demo-btn {
        width: 100%;
    }
}
