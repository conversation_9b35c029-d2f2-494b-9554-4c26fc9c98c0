@model HotelBooking.Models.ViewModels.PaymentListViewModel
@{
    ViewData["Title"] = "Payment Processing";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Payment Processing</h3>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" name="searchTerm" value="@Model.SearchTerm" class="form-control" placeholder="Search payments..." />
                            </div>
                            <div class="col-md-2">
                                <select name="statusFilter" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="Completed" selected="@(Model.StatusFilter == "Completed")">Completed</option>
                                    <option value="Pending" selected="@(Model.StatusFilter == "Pending")">Pending</option>
                                    <option value="Failed" selected="@(Model.StatusFilter == "Failed")">Failed</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" name="fromDate" value="@Model.FromDate?.ToString("yyyy-MM-dd")" class="form-control" />
                            </div>
                            <div class="col-md-2">
                                <input type="date" name="toDate" value="@Model.ToDate?.ToString("yyyy-MM-dd")" class="form-control" />
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary">Search</button>
                            </div>
                            <div class="col-md-1">
                                <a href="@Url.Action("Index")" class="btn btn-secondary">Clear</a>
                            </div>
                        </div>
                    </form>

                    <!-- Statistics -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5>Total Payments</h5>
                                    <h3>@Model.TotalPayments</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5>Total Amount</h5>
                                    <h3>@Model.TotalAmount.ToString("C")</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment List Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Payment ID</th>
                                    <th>Guest Name</th>
                                    <th>Room</th>
                                    <th>Amount</th>
                                    <th>Payment Method</th>
                                    <th>Payment Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Payments != null && Model.Payments.Any())
                                {
                                    @foreach (var payment in Model.Payments)
                                    {
                                        <tr>
                                            <td>@payment.PaymentID</td>
                                            <td>@payment.GuestName</td>
                                            <td>@payment.RoomNumber (@payment.RoomType)</td>
                                            <td>@payment.Amount.ToString("C")</td>
                                            <td>@payment.PaymentMethod</td>
                                            <td>@payment.PaymentDate.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>
                                                <span class="badge @(payment.Status == "Completed" ? "bg-success" : payment.Status == "Pending" ? "bg-warning" : "bg-danger")">
                                                    @payment.Status
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("Refund", new { id = payment.PaymentID })" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-undo"></i> Refund
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="8" class="text-center">No payments found.</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if (Model.TotalPages > 1)
                    {
                        <nav aria-label="Payment pagination">
                            <ul class="pagination justify-content-center">
                                @if (Model.CurrentPage > 1)
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage - 1, searchTerm = Model.SearchTerm, statusFilter = Model.StatusFilter, fromDate = Model.FromDate, toDate = Model.ToDate })">Previous</a>
                                    </li>
                                }

                                @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                {
                                    <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                        <a class="page-link" href="@Url.Action("Index", new { page = i, searchTerm = Model.SearchTerm, statusFilter = Model.StatusFilter, fromDate = Model.FromDate, toDate = Model.ToDate })">@i</a>
                                    </li>
                                }

                                @if (Model.CurrentPage < Model.TotalPages)
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage + 1, searchTerm = Model.SearchTerm, statusFilter = Model.StatusFilter, fromDate = Model.FromDate, toDate = Model.ToDate })">Next</a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>
}
