using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using HotelBooking.Data;
using HotelBooking.Models;
using HotelBooking.Models.ViewModels;

namespace HotelBooking.Controllers
{
    [Authorize(Roles = "Admin,Staff")]
    public class NotificationController : Controller
    {
        private readonly HotelBookingContext _context;

        public NotificationController(HotelBookingContext context)
        {
            _context = context;
        }

        // GET: Notification
        public async Task<IActionResult> Index(string searchTerm, string typeFilter, string statusFilter, DateTime? fromDate, DateTime? toDate, int page = 1, int pageSize = 10)
        {
            var query = _context.Notifications
                .Include(n => n.User)
                .AsQueryable();

            // Search
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(n => n.Title!.Contains(searchTerm) ||
                                        n.Message!.Contains(searchTerm) ||
                                        n.User!.UserName!.Contains(searchTerm));
            }

            // Type filter
            if (!string.IsNullOrEmpty(typeFilter))
            {
                query = query.Where(n => n.Type == typeFilter);
            }

            // Status filter
            if (!string.IsNullOrEmpty(statusFilter))
            {
                query = query.Where(n => n.Status == statusFilter);
            }

            // Date filter
            if (fromDate.HasValue)
            {
                query = query.Where(n => n.CreatedDate >= fromDate.Value);
            }
            if (toDate.HasValue)
            {
                query = query.Where(n => n.CreatedDate <= toDate.Value);
            }

            var totalNotifications = await query.CountAsync();

            var notifications = await query
                .OrderByDescending(n => n.CreatedDate)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(n => new NotificationViewModel
                {
                    NotificationID = n.NotificationID,
                    UserID = n.UserID,
                    Title = n.Title,
                    Message = n.Message,
                    Type = n.Type,
                    Status = n.Status,
                    CreatedDate = n.CreatedDate,
                    SentDate = n.SentDate,
                    IsRead = n.IsRead,
                    UserName = n.User!.UserName,
                    UserEmail = n.User.Email
                })
                .ToListAsync();

            var viewModel = new NotificationListViewModel
            {
                Notifications = notifications,
                TotalNotifications = totalNotifications,
                CurrentPage = page,
                TotalPages = (int)Math.Ceiling((double)totalNotifications / pageSize),
                SearchTerm = searchTerm,
                TypeFilter = typeFilter,
                StatusFilter = statusFilter,
                FromDate = fromDate,
                ToDate = toDate
            };

            return View(viewModel);
        }

        // GET: Notification/Send
        public async Task<IActionResult> Send()
        {
            var viewModel = new SendNotificationViewModel
            {
                SendImmediately = true,
                Users = await _context.Users.Where(u => u.IsActive).ToListAsync(),
                Roles = await _context.Roles.Where(r => r.IsActive).ToListAsync()
            };

            return View(viewModel);
        }

        // POST: Notification/Send
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Send(SendNotificationViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var userIds = new List<int>();

                    // Determine target users
                    switch (viewModel.SendTo)
                    {
                        case "All":
                            userIds = await _context.Users.Where(u => u.IsActive).Select(u => u.Id).ToListAsync();
                            break;
                        case "Specific":
                            userIds = viewModel.UserIDs ?? new List<int>();
                            break;
                        case "Role":
                            if (viewModel.RoleID.HasValue)
                            {
                                userIds = await _context.Users
                                    .Where(u => u.IsActive && u.CustomRoleId == viewModel.RoleID.Value)
                                    .Select(u => u.Id)
                                    .ToListAsync();
                            }
                            break;
                    }

                    // Create notifications for each user
                    var notifications = new List<Notification>();
                    foreach (var userId in userIds)
                    {
                        var notification = new Notification
                        {
                            UserID = userId,
                            Title = viewModel.Title,
                            Message = viewModel.Message,
                            Type = viewModel.Type,
                            Status = viewModel.SendImmediately ? "Sent" : "Pending",
                            CreatedDate = DateTime.Now,
                            SentDate = viewModel.SendImmediately ? DateTime.Now : viewModel.ScheduleDate,
                            IsRead = false,
                            CreatedBy = User.Identity?.Name
                        };
                        notifications.Add(notification);
                    }

                    _context.Notifications.AddRange(notifications);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = $"Notification sent to {notifications.Count} users successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "An error occurred while sending notifications: " + ex.Message);
                }
            }

            viewModel.Users = await _context.Users.Where(u => u.IsActive).ToListAsync();
            viewModel.Roles = await _context.Roles.Where(r => r.IsActive).ToListAsync();
            return View(viewModel);
        }

        // GET: Notification/Bulk
        public async Task<IActionResult> Bulk()
        {
            var viewModel = new BulkNotificationViewModel
            {
                Roles = await _context.Roles.Where(r => r.IsActive).ToListAsync()
            };

            return View(viewModel);
        }

        // POST: Notification/Bulk
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Bulk(BulkNotificationViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var userIds = new List<int>();

                    // Determine target users based on audience
                    switch (viewModel.TargetAudience)
                    {
                        case "AllUsers":
                            userIds = await _context.Users
                                .Where(u => viewModel.IncludeInactiveUsers || u.IsActive)
                                .Select(u => u.Id)
                                .ToListAsync();
                            break;
                        case "AllGuests":
                            userIds = await _context.Users
                                .Where(u => (viewModel.IncludeInactiveUsers || u.IsActive) && u.CustomRoleId == 2) // Customer role
                                .Select(u => u.Id)
                                .ToListAsync();
                            break;
                        case "AllStaff":
                            userIds = await _context.Users
                                .Where(u => (viewModel.IncludeInactiveUsers || u.IsActive) && (u.CustomRoleId == 1 || u.CustomRoleId == 3)) // Admin or Staff
                                .Select(u => u.Id)
                                .ToListAsync();
                            break;
                        case "SpecificRole":
                            if (viewModel.RoleID.HasValue)
                            {
                                userIds = await _context.Users
                                    .Where(u => (viewModel.IncludeInactiveUsers || u.IsActive) && u.CustomRoleId == viewModel.RoleID.Value)
                                    .Select(u => u.Id)
                                    .ToListAsync();
                            }
                            break;
                    }

                    // Create notifications
                    var notifications = new List<Notification>();
                    foreach (var userId in userIds)
                    {
                        var notification = new Notification
                        {
                            UserID = userId,
                            Title = viewModel.Title,
                            Message = viewModel.Message,
                            Type = viewModel.Type,
                            Status = "Sent",
                            CreatedDate = DateTime.Now,
                            SentDate = DateTime.Now,
                            IsRead = false,
                            CreatedBy = User.Identity?.Name
                        };
                        notifications.Add(notification);
                    }

                    _context.Notifications.AddRange(notifications);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = $"Bulk notification sent to {notifications.Count} users successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "An error occurred while sending bulk notifications: " + ex.Message);
                }
            }

            viewModel.Roles = await _context.Roles.Where(r => r.IsActive).ToListAsync();
            return View(viewModel);
        }

        // GET: Notification/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var notification = await _context.Notifications
                .Include(n => n.User)
                .FirstOrDefaultAsync(n => n.NotificationID == id);

            if (notification == null)
            {
                return NotFound();
            }

            var viewModel = new NotificationViewModel
            {
                NotificationID = notification.NotificationID,
                UserID = notification.UserID,
                Title = notification.Title,
                Message = notification.Message,
                Type = notification.Type,
                Status = notification.Status,
                CreatedDate = notification.CreatedDate,
                SentDate = notification.SentDate,
                IsRead = notification.IsRead,
                UserName = notification.User?.UserName,
                UserEmail = notification.User?.Email
            };

            return View(viewModel);
        }

        // POST: Notification/MarkAsRead/5
        [HttpPost]
        public async Task<IActionResult> MarkAsRead(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification != null)
            {
                notification.IsRead = true;
                await _context.SaveChangesAsync();
            }

            return Json(new { success = true });
        }

        // POST: Notification/Delete/5
        [HttpPost]
        public async Task<IActionResult> Delete(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification != null)
            {
                _context.Notifications.Remove(notification);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Notification deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Notification/Templates
        public IActionResult Templates()
        {
            var templates = new List<NotificationTemplateViewModel>
            {
                new NotificationTemplateViewModel
                {
                    TemplateName = "Booking Confirmation",
                    Title = "Booking Confirmed - {ReservationID}",
                    Message = "Dear {GuestName}, your booking for {RoomType} room {RoomNumber} from {CheckInDate} to {CheckOutDate} has been confirmed.",
                    Type = "Email"
                },
                new NotificationTemplateViewModel
                {
                    TemplateName = "Payment Reminder",
                    Title = "Payment Reminder - {ReservationID}",
                    Message = "Dear {GuestName}, this is a reminder that payment of {Amount} is due for your reservation {ReservationID}.",
                    Type = "Email"
                },
                new NotificationTemplateViewModel
                {
                    TemplateName = "Check-in Reminder",
                    Title = "Check-in Reminder - Tomorrow",
                    Message = "Dear {GuestName}, this is a reminder that your check-in is scheduled for tomorrow at {CheckInDate}. Room {RoomNumber} will be ready for you.",
                    Type = "SMS"
                }
            };

            return View(templates);
        }
    }
}
