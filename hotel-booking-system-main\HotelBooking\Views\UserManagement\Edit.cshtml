@model HotelBooking.Models.ViewModels.EditUserViewModel
@{
    ViewData["Title"] = "Edit User";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-edit"></i> Edit User
                    </h3>
                </div>

                <form asp-action="Edit" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <input type="hidden" asp-for="UserId" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="UserName" class="control-label"></label>
                                    <input asp-for="UserName" class="form-control" readonly />
                                    <span asp-validation-for="UserName" class="text-danger"></span>
                                    <small class="form-text text-muted">Username cannot be changed</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Email" class="control-label"></label>
                                    <input asp-for="Email" class="form-control" />
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="PhoneNumber" class="control-label"></label>
                                    <input asp-for="PhoneNumber" class="form-control" />
                                    <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="RoleId" class="control-label"></label>
                                    <select asp-for="RoleId" class="form-control">
                                        <option value="">Select Role</option>
                                        @foreach (var role in Model.Roles)
                                        {
                                            <option value="@role.Id" selected="@(Model.RoleId == role.Id)">
                                                @role.RoleName
                                            </option>
                                        }
                                    </select>
                                    <span asp-validation-for="RoleId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                <label asp-for="IsActive" class="form-check-label"></label>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Note:</strong> To change the user's password, use the "Reset Password" function from the user details page.
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update User
                        </button>
                        <a href="@Url.Action("Details", new { id = Model.UserId })" class="btn btn-info">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        <a href="@Url.Action("Index")" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
