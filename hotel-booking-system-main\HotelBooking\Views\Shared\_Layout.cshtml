﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - HotelBooking</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/HotelBooking.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        /* Sticky Footer Layout */
        * {
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .main-content {
            flex: 1 0 auto;
        }

        .modern-footer {
            flex-shrink: 0;
            margin-top: 0 !important;
        }

        /* Reset any conflicting Bootstrap/custom styles */
        .pb-3 {
            padding-bottom: 0 !important;
        }

        .hotel-management-container {
            min-height: auto !important;
            height: auto !important;
        }

        /* Ensure proper spacing */
        .container, .container-fluid {
            padding-bottom: 0;
        }
    </style>
</head>
<body>
    <header>
        <div style="background: #f8f9fa; border-bottom: 1px solid #dee2e6; padding: 15px 0;">
            <div style="display: flex; align-items: center; justify-content: space-between; max-width: 1200px; margin: 0 auto; padding: 0 20px;">
                <!-- Brand -->
                <div>
                    <a asp-controller="Home" asp-action="Index" style="text-decoration: none; color: #007bff; font-size: 1.5rem; font-weight: bold;">
                        <i class="fas fa-building me-2"></i>HotelBooking
                    </a>
                </div>

                <!-- Navigation Items -->
                <div style="display: flex; gap: 20px;">
                    <a asp-controller="Dashboard" asp-action="Index" class="nav-item-simple">
                        <i class="fas fa-chart-line"></i>
                        <span>Dashboard</span>
                    </a>
                    <a asp-controller="Home" asp-action="Index" class="nav-item-simple">
                        <i class="fas fa-home"></i>
                        <span>Hotel Management</span>
                    </a>
                    <a asp-controller="Hotels" asp-action="Index" class="nav-item-simple">
                        <i class="fas fa-building"></i>
                        <span>Hotels</span>
                    </a>
                    <a asp-controller="Rooms" asp-action="Index" class="nav-item-simple">
                        <i class="fas fa-bed"></i>
                        <span>Rooms</span>
                    </a>
                    <a asp-controller="Reservations" asp-action="Index" class="nav-item-simple">
                        <i class="fas fa-calendar-check"></i>
                        <span>Reservations</span>
                    </a>
                    <a asp-controller="Guests" asp-action="Index" class="nav-item-simple">
                        <i class="fas fa-users"></i>
                        <span>Guests</span>
                    </a>
                    @if (User.Identity!.IsAuthenticated)
                    {
                        <a asp-controller="Reservations" asp-action="Index" class="nav-item-simple">
                            <i class="fas fa-bookmark"></i>
                            <span>My Bookings</span>
                        </a>
                    }
                </div>

                <!-- User Menu -->
                <div>
                    @if (User.Identity!.IsAuthenticated)
                    {
                        <div class="dropdown">
                            <button class="btn btn-link dropdown-toggle p-0" type="button" id="userDropdown" data-bs-toggle="dropdown" style="text-decoration: none; color: #6c757d;">
                                <i class="fas fa-user-circle me-1"></i>Welcome, @User.Identity.Name
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" asp-controller="Reservations" asp-action="Index">
                                        <i class="fas fa-calendar-check me-2"></i>My Reservations
                                    </a>
                                </li>
                                @if (User.IsInRole("Admin"))
                                {
                                    <li>
                                        <a class="dropdown-item" asp-controller="Admin" asp-action="Dashboard">
                                            <i class="fas fa-tachometer-alt me-2"></i>Admin Panel
                                        </a>
                                    </li>
                                }
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    }
                    else
                    {
                        <div style="display: flex; gap: 10px;">
                            <a asp-controller="Account" asp-action="Login" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                            <a href="/Account/Register" class="btn btn-primary btn-sm">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </header>
    <div class="main-content">
        <div class="@(ViewData["Title"]?.ToString() == "Hotel Management" ? "" : "container")">
            <main role="main" class="pb-3">
                @if (TempData["Message"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show fs-5 py-3" role="alert">
                        <i class="fas fa-check-circle me-3"></i>@TempData["Message"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                }
                @if (TempData["Error"] != null)
                {
                    <div class="alert alert-danger alert-dismissible fade show fs-5 py-3" role="alert">
                        <i class="fas fa-exclamation-circle me-3"></i>@TempData["Error"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                }
                @RenderBody()
            </main>
        </div>
    </div>

    <!-- Simple Footer -->
    <footer class="modern-footer">
        <div class="footer-main">
            <div class="container">
                <div class="row">
                    <div class="col-md-3">
                        <div class="footer-brand">
                            <h5 class="footer-title">
                                <i class="fas fa-building me-2"></i>HotelBooking
                            </h5>
                            <p class="footer-description">
                                Professional hotel booking system for modern hospitality.
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="footer-links-simple">
                            <h6 style="color: #3498db; margin-bottom: 15px; font-weight: 600;">Quick Links</h6>
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <a asp-controller="Dashboard" asp-action="Index">Dashboard</a>
                                <a asp-controller="Rooms" asp-action="Index">Rooms</a>
                                <a asp-controller="Reservations" asp-action="Index">Reservations</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h6 style="color: #3498db; margin-bottom: 15px; font-weight: 600;">Contact Us</h6>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <div style="display: flex; align-items: center;">
                                <i class="fas fa-envelope me-2" style="color: #3498db; width: 20px;"></i>
                                <a href="mailto:<EMAIL>" style="color: #bdc3c7; text-decoration: none; font-size: 0.9rem;">
                                    <EMAIL>
                                </a>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <i class="fas fa-phone me-2" style="color: #3498db; width: 20px;"></i>
                                <a href="tel:+84868686899" style="color: #bdc3c7; text-decoration: none; font-size: 0.9rem;">
                                    +84 868 686 899
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h6 style="color: #3498db; margin-bottom: 15px; font-weight: 600;">About Us</h6>
                        <p style="color: #bdc3c7; font-size: 0.85rem; line-height: 1.5; margin-bottom: 10px;">
                            Your trusted partner in hospitality management, providing innovative booking solutions worldwide.
                        </p>
                        <div class="social-links-simple">
                            <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="copyright">
                            <i class="fas fa-copyright me-2"></i>
                            2025 <strong>HotelBooking</strong>. All rights reserved.
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="footer-bottom-links">
                            <a asp-controller="Home" asp-action="Privacy">Privacy</a>
                            <a href="#">Terms</a>
                            <a href="#">Support</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </footer>

    <!-- Header & Footer Styles -->
    <style>
        /* Header Navigation Styles */
        .nav-item-simple {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #6c757d;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            min-width: 70px;
        }

        .nav-item-simple:hover {
            color: #007bff;
            background-color: #e7f3ff;
            text-decoration: none;
        }

        .nav-item-simple i {
            font-size: 1.1rem;
            margin-bottom: 4px;
        }

        .nav-item-simple span {
            font-weight: 500;
            white-space: nowrap;
        }

        /* Responsive adjustments */
        @@media (max-width: 992px) {
            .nav-item-simple {
                font-size: 0.75rem;
                padding: 6px 8px;
                min-width: 60px;
            }

            .nav-item-simple i {
                font-size: 1rem;
            }
        }

        @@media (max-width: 768px) {
            .nav-item-simple span {
                display: none;
            }

            .nav-item-simple {
                min-width: 35px;
                padding: 8px 6px;
            }
        }

        .modern-footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: #ecf0f1;
            width: 100%;
            position: relative;
            z-index: 10;
        }

        .footer-main {
            padding: 40px 0 20px;
        }

        .footer-brand {
            margin-bottom: 0;
        }

        .footer-title {
            color: #3498db;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .footer-description {
            color: #bdc3c7;
            line-height: 1.5;
            margin-bottom: 0;
            font-size: 0.9rem;
        }

        .footer-links-simple {
            display: block;
        }

        .footer-links-simple a {
            color: #bdc3c7;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .footer-links-simple a:hover {
            color: #3498db;
        }

        .social-links-simple {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .social-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid #3498db;
            border-radius: 50%;
            color: #3498db;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .social-link:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
        }

        .footer-bottom {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .copyright {
            color: #bdc3c7;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .footer-bottom-links {
            display: flex;
            justify-content: flex-end;
            gap: 25px;
            flex-wrap: wrap;
        }

        .footer-bottom-links a {
            color: #bdc3c7;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .footer-bottom-links a:hover {
            color: #3498db;
        }



        /* Responsive styles */
        @@media (max-width: 768px) {
            .footer-main {
                padding: 30px 0 15px;
            }

            .footer-title {
                font-size: 1.3rem;
            }

            .social-links-simple {
                justify-content: center;
            }

            .footer-bottom-links {
                justify-content: center;
                margin-top: 10px;
            }

            .col-md-3 {
                margin-bottom: 25px;
                text-align: center;
            }
        }

        /* Animation for footer reveal */
        .modern-footer {
            animation: fadeInUp 0.6s ease-out;
        }

        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
