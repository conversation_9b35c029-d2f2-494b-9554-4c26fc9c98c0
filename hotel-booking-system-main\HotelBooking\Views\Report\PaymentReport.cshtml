@model HotelBooking.Models.ViewModels.PaymentReportViewModel
@{
    ViewData["Title"] = "Payment Report";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-credit-card me-2"></i>Payment Report</h2>
                <div>
                    <button onclick="window.print()" class="btn btn-secondary me-2">
                        <i class="fas fa-print me-2"></i>Print Report
                    </button>
                    <a href="@Url.Action("Index", "Report")" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">$@Model.TotalRevenue.ToString("N2")</h4>
                            <p class="card-text">Total Revenue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">@Model.TotalTransactions</h4>
                            <p class="card-text">Total Transactions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exchange-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">$@Model.AverageTransactionValue.ToString("N2")</h4>
                            <p class="card-text">Average Transaction</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">$@Model.PendingPayments.ToString("N2")</h4>
                            <p class="card-text">Pending Payments</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Payment Methods Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie me-2"></i>Payment Methods</h5>
                </div>
                <div class="card-body">
                    <canvas id="paymentMethodsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Monthly Revenue Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar me-2"></i>Monthly Revenue</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyRevenueChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods Performance -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-credit-card me-2"></i>Payment Methods Performance</h5>
                </div>
                <div class="card-body">
                    @if (Model.PaymentMethodData != null && Model.PaymentMethodData.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Payment Method</th>
                                        <th>Transaction Count</th>
                                        <th>Total Amount</th>
                                        <th>Average Amount</th>
                                        <th>Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var method in Model.PaymentMethodData)
                                    {
                                        <tr>
                                            <td>
                                                <i class="fas fa-@(method.PaymentMethod?.ToLower() == "credit card" ? "credit-card" : 
                                                                   method.PaymentMethod?.ToLower() == "cash" ? "money-bill" : 
                                                                   method.PaymentMethod?.ToLower() == "bank transfer" ? "university" : "wallet") me-2"></i>
                                                @method.PaymentMethod
                                            </td>
                                            <td>@method.TransactionCount</td>
                                            <td>$@method.TotalAmount.ToString("N2")</td>
                                            <td>$@method.AverageAmount.ToString("N2")</td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: @method.Percentage.ToString("F1")%" 
                                                         aria-valuenow="@method.Percentage" aria-valuemin="0" aria-valuemax="100">
                                                        @method.Percentage.ToString("F1")%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">No payment method data available for the selected period.</p>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history me-2"></i>Recent Transactions</h5>
                </div>
                <div class="card-body">
                    @if (Model.RecentTransactions != null && Model.RecentTransactions.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Guest</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var transaction in Model.RecentTransactions.Take(10))
                                    {
                                        <tr>
                                            <td>@transaction.PaymentDate.ToString("MMM dd, yyyy HH:mm")</td>
                                            <td>@transaction.GuestName</td>
                                            <td>$@transaction.Amount.ToString("N2")</td>
                                            <td>
                                                <i class="fas fa-@(transaction.PaymentMethod?.ToLower() == "credit card" ? "credit-card" : 
                                                                   transaction.PaymentMethod?.ToLower() == "cash" ? "money-bill" : 
                                                                   transaction.PaymentMethod?.ToLower() == "bank transfer" ? "university" : "wallet") me-2"></i>
                                                @transaction.PaymentMethod
                                            </td>
                                            <td>
                                                <span class="badge bg-success">Completed</span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewTransaction(@transaction.PaymentBatchID)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">No recent transactions available.</p>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Revenue Trends -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line me-2"></i>Daily Revenue Trends</h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyTrendsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Payment Methods Chart
        const methodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
        const methodsData = @Html.Raw(Json.Serialize(Model.PaymentMethodData != null ? Model.PaymentMethodData : new List<object>()));

        new Chart(methodsCtx, {
            type: 'doughnut',
            data: {
                labels: methodsData.map(d => d.paymentMethod),
                datasets: [{
                    data: methodsData.map(d => d.totalAmount),
                    backgroundColor: [
                        '#007bff', // Blue
                        '#28a745', // Green
                        '#ffc107', // Yellow
                        '#dc3545', // Red
                        '#6f42c1', // Purple
                        '#fd7e14'  // Orange
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': $' + context.parsed.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Monthly Revenue Chart
        const revenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
        const revenueData = @Html.Raw(Json.Serialize(Model.MonthlyData != null ? Model.MonthlyData : new List<object>()));

        new Chart(revenueCtx, {
            type: 'bar',
            data: {
                labels: revenueData.map(d => d.month),
                datasets: [{
                    label: 'Revenue ($)',
                    data: revenueData.map(d => d.revenue),
                    backgroundColor: '#28a745',
                    borderColor: '#1e7e34',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Daily Trends Chart
        const dailyCtx = document.getElementById('dailyTrendsChart').getContext('2d');
        const dailyData = @Html.Raw(Json.Serialize(Model.DailyData != null ? Model.DailyData : new List<object>()));
        
        new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: dailyData.map(d => d.date),
                datasets: [{
                    label: 'Daily Revenue ($)',
                    data: dailyData.map(d => d.revenue),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        function viewTransaction(id) {
            // Implement transaction details view
            alert('View transaction details for ID: ' + id);
        }
    </script>
}

<style>
    @@media print {
        .btn, .navbar, .sidebar {
            display: none !important;
        }
        .container-fluid {
            margin: 0 !important;
            padding: 0 !important;
        }
    }
</style>
