@model HotelBooking.Models.ViewModels.CreateUserViewModel
@{
    ViewData["Title"] = "Create User";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-plus"></i> Create New User
                    </h3>
                </div>

                <form asp-action="Create" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="form-group">
                            <label asp-for="Email" class="control-label"></label>
                            <input asp-for="Email" class="form-control" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="Password" class="control-label"></label>
                            <input asp-for="Password" class="form-control" type="password" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="ConfirmPassword" class="control-label"></label>
                            <input asp-for="ConfirmPassword" class="form-control" type="password" />
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="PhoneNumber" class="control-label"></label>
                            <input asp-for="PhoneNumber" class="form-control" />
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="RoleId" class="control-label"></label>
                            <select asp-for="RoleId" class="form-control">
                                <option value="">Select Role</option>
                                @foreach (var role in Model.Roles)
                                {
                                    <option value="@role.Id">@role.RoleName</option>
                                }
                            </select>
                            <span asp-validation-for="RoleId" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                <label asp-for="IsActive" class="form-check-label"></label>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create User
                        </button>
                        <a href="@Url.Action("Index")" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
