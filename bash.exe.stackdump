Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF854FF0000 ntdll.dll
7FF8547B0000 KERNEL32.DLL
7FF8528A0000 KERNELBASE.dll
7FF853970000 USER32.dll
7FF8525E0000 win32u.dll
7FF853D10000 GDI32.dll
7FF8522A0000 gdi32full.dll
7FF852540000 msvcp_win.dll
7FF852610000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF854880000 advapi32.dll
7FF8537C0000 msvcrt.dll
7FF8549A0000 sechost.dll
7FF852510000 bcrypt.dll
7FF8536A0000 RPCRT4.dll
7FF851870000 CRYPTBASE.DLL
7FF8520E0000 bcryptPrimitives.dll
7FF853EC0000 IMM32.DLL
