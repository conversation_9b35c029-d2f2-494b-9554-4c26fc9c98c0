<!DOCTYPE html>
<html>
<head>
    <title>Tạo <PERSON> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <h2 class="text-center mb-4">👑 Tạo Tài Khoản Admin</h2>
                
                <div class="card">
                    <div class="card-body">
                        <form id="createAdminForm">
                            <div class="mb-3">
                                <label class="form-label">Email:</label>
                                <input type="email" class="form-control" id="email" value="<EMAIL>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Username:</label>
                                <input type="text" class="form-control" id="username" value="admin" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Password:</label>
                                <input type="password" class="form-control" id="password" value="123456" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">👑 Tạo Admin Account</button>
                        </form>
                    </div>
                </div>

                <div id="result" class="mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5>Kết quả</h5>
                        </div>
                        <div class="card-body">
                            <div id="message"></div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h5>Tài khoản mẫu:</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <button class="btn btn-outline-danger btn-sm w-100 mb-2" onclick="fillForm('<EMAIL>', 'admin', '123456')">
                                Admin Đơn Giản
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-outline-warning btn-sm w-100 mb-2" onclick="fillForm('<EMAIL>', 'superadmin', 'admin123')">
                                Super Admin
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mt-4 alert alert-info">
                    <h6>📝 Lưu ý:</h6>
                    <ul class="mb-0">
                        <li>Tài khoản sẽ được tạo trực tiếp vào database SQL Server</li>
                        <li>Tài khoản có quyền Admin (toàn quyền hệ thống)</li>
                        <li>Email phải là duy nhất</li>
                        <li>Password tối thiểu 6 ký tự</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function fillForm(email, username, password) {
            $('#email').val(email);
            $('#username').val(username);
            $('#password').val(password);
        }

        $('#createAdminForm').on('submit', function(e) {
            e.preventDefault();
            
            const email = $('#email').val();
            const username = $('#username').val();
            const password = $('#password').val();
            
            if (!email || !username || !password) {
                alert('Vui lòng điền đầy đủ thông tin!');
                return;
            }
            
            $.post('/Account/CreateAdminAccount', {
                email: email,
                username: username,
                password: password
            })
            .done(function(data) {
                $('#result').show();
                
                if (data.success) {
                    $('#message').html(`
                        <div class="alert alert-success">
                            <h6>${data.message}</h6>
                            <hr>
                            <strong>Thông tin đăng nhập:</strong><br>
                            <strong>Email:</strong> ${data.userInfo.email}<br>
                            <strong>Username:</strong> ${data.userInfo.username}<br>
                            <strong>Password:</strong> ${data.userInfo.password}<br>
                            <strong>Role:</strong> ${data.userInfo.role}
                            <hr>
                            <a href="/Account/Login" class="btn btn-success btn-sm">🔐 Đăng nhập ngay</a>
                        </div>
                    `);
                } else {
                    $('#message').html(`
                        <div class="alert alert-danger">
                            ${data.message}
                        </div>
                    `);
                }
            })
            .fail(function() {
                $('#result').show();
                $('#message').html('<div class="alert alert-danger">❌ Lỗi khi gọi API tạo admin</div>');
            });
        });
    </script>
</body>
</html>
