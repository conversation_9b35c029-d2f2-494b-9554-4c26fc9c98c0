@model HotelBooking.Models.Amenity
@{
    ViewData["Title"] = "Amenity Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        @if (!string.IsNullOrEmpty(Model.Icon))
                        {
                            <i class="@Model.Icon"></i>
                        }
                        else
                        {
                            <i class="fas fa-star"></i>
                        }
                        @Model.AmenityName
                    </h3>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Name:</dt>
                                <dd class="col-sm-8">@Model.AmenityName</dd>

                                <dt class="col-sm-4">Category:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge badge-secondary">@Model.Category</span>
                                </dd>

                                <dt class="col-sm-4">Icon:</dt>
                                <dd class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.Icon))
                                    {
                                        <i class="@Model.Icon"></i> @Model.Icon
                                    }
                                    else
                                    {
                                        <span class="text-muted">No icon</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge badge-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-danger">Inactive</span>
                                    }
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Description:</dt>
                                <dd class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.Description))
                                    {
                                        @Model.Description
                                    }
                                    else
                                    {
                                        <span class="text-muted">No description</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">Created Date:</dt>
                                <dd class="col-sm-8">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</dd>

                                <dt class="col-sm-4">Created By:</dt>
                                <dd class="col-sm-8">@(Model.CreatedBy ?? "System")</dd>

                                @if (Model.ModifiedDate.HasValue)
                                {
                                    <dt class="col-sm-4">Modified Date:</dt>
                                    <dd class="col-sm-8">@Model.ModifiedDate.Value.ToString("dd/MM/yyyy HH:mm")</dd>

                                    <dt class="col-sm-4">Modified By:</dt>
                                    <dd class="col-sm-8">@(Model.ModifiedBy ?? "System")</dd>
                                }
                            </dl>
                        </div>
                    </div>

                    @if (Model.RoomAmenities != null && Model.RoomAmenities.Any())
                    {
                        <hr />
                        <h5><i class="fas fa-bed"></i> Rooms with this Amenity</h5>
                        <div class="row">
                            @foreach (var roomAmenity in Model.RoomAmenities)
                            {
                                <div class="col-md-3 mb-2">
                                    <div class="card card-outline card-info">
                                        <div class="card-body p-2">
                                            <h6 class="card-title mb-1">
                                                Room @roomAmenity.Room?.RoomNumber
                                            </h6>
                                            <p class="card-text small mb-1">
                                                <strong>Type:</strong> @roomAmenity.Room?.RoomType?.TypeName<br>
                                                <strong>Price:</strong> $@roomAmenity.Room?.Price
                                            </p>
                                            <a href="@Url.Action("Details", "Rooms", new { id = roomAmenity.Room?.RoomID })" 
                                               class="btn btn-sm btn-outline-primary">
                                                View Room
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <hr />
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            This amenity is not assigned to any rooms yet.
                        </div>
                    }
                </div>

                <div class="card-footer">
                    @if (User.IsInRole("Admin") || User.IsInRole("Staff"))
                    {
                        <a href="@Url.Action("Edit", new { id = Model.AmenityID })" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="@Url.Action("AssignToRoom", new { id = Model.AmenityID })" class="btn btn-primary">
                            <i class="fas fa-link"></i> Assign to Rooms
                        </a>
                        <button type="button" class="btn btn-@(Model.IsActive ? "secondary" : "success")" 
                                onclick="toggleAmenityStatus(@Model.AmenityID)">
                            @if (Model.IsActive)
                            {
                                <i class="fas fa-ban"></i> @("Deactivate")
                            }
                            else
                            {
                                <i class="fas fa-check"></i> @("Activate")
                            }
                        </button>
                        @if (User.IsInRole("Admin"))
                        {
                            <a href="@Url.Action("Delete", new { id = Model.AmenityID })" 
                               class="btn btn-danger"
                               onclick="return confirm('Are you sure you want to delete this amenity?')">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                        }
                    }
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function toggleAmenityStatus(amenityId) {
            $.post('@Url.Action("ToggleStatus")', { id: amenityId }, function(result) {
                if (result.success) {
                    location.reload();
                } else {
                    alert('Error: ' + result.message);
                }
            });
        }
    </script>
}
