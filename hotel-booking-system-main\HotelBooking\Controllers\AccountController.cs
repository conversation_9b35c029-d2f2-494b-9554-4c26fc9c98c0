using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication;
using HotelBooking.Models;
using HotelBooking.Data;

namespace HotelBooking.Controllers
{
    public class AccountController : Controller
    {
        private readonly UserManager<CustomUser> _userManager;
        private readonly SignInManager<CustomUser> _signInManager;
        private readonly RoleManager<CustomRole> _roleManager;
        private readonly HotelBookingContext _context;

        public AccountController(
            UserManager<CustomUser> userManager,
            SignInManager<CustomUser> signInManager,
            RoleManager<CustomRole> roleManager,
            HotelBookingContext context)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _roleManager = roleManager;
            _context = context;
        }
        [HttpGet]
        public IActionResult Login(string? returnUrl = null)
        {
            ViewData["ReturnUrl"] = returnUrl;
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginViewModel model, string? returnUrl = null)
        {
            ViewData["ReturnUrl"] = returnUrl;

            if (ModelState.IsValid)
            {
                Microsoft.AspNetCore.Identity.SignInResult result;
                CustomUser? user = null;

                // Thử đăng nhập bằng email trước
                user = await _userManager.FindByEmailAsync(model.Email!);
                if (user != null)
                {
                    result = await _signInManager.PasswordSignInAsync(user.UserName!, model.Password!, model.RememberMe, lockoutOnFailure: false);
                }
                else
                {
                    // Nếu không tìm thấy bằng email, thử bằng username
                    user = await _userManager.FindByNameAsync(model.Email!);
                    if (user != null)
                    {
                        result = await _signInManager.PasswordSignInAsync(user.UserName!, model.Password!, model.RememberMe, lockoutOnFailure: false);
                    }
                    else
                    {
                        ModelState.AddModelError(string.Empty, "User not found with email or username: " + model.Email);
                        return View(model);
                    }
                }

                if (result.Succeeded)
                {
                    TempData["Message"] = $"Welcome back, {user?.UserName}!";

                    if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
                    {
                        return Redirect(returnUrl);
                    }
                    return RedirectToAction("Index", "Home");
                }
                else
                {
                    var errorMsg = "Invalid password.";
                    if (result.IsNotAllowed)
                        errorMsg = "Account not allowed to sign in.";
                    else if (result.IsLockedOut)
                        errorMsg = "Account is locked out.";
                    else if (result.RequiresTwoFactor)
                        errorMsg = "Two-factor authentication required.";

                    ModelState.AddModelError(string.Empty, errorMsg);
                }
            }

            return View(model);
        }



        [HttpGet]
        public IActionResult Register()
        {
            return View();
        }

        [HttpGet]
        public IActionResult RegisterSimple()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Register(RegisterViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = new CustomUser
                {
                    UserName = model.Email,
                    Email = model.Email,
                    PhoneNumber = model.PhoneNumber,
                    CreatedAt = DateTime.Now,
                    IsActive = true,
                    CreatedBy = "System",
                    CreatedDate = DateTime.Now,
                    CustomRoleId = 2 // Default to Customer role
                };

                var result = await _userManager.CreateAsync(user, model.Password!);

                if (result.Succeeded)
                {
                    // Ensure Customer role exists
                    var roleExists = await _roleManager.RoleExistsAsync("Customer");
                    if (!roleExists)
                    {
                        var customerRole = new CustomRole
                        {
                            Name = "Customer",
                            NormalizedName = "CUSTOMER",
                            RoleName = "Customer",
                            Description = "Customer role",
                            IsActive = true,
                            CreatedBy = "System",
                            CreatedDate = DateTime.Now
                        };
                        await _roleManager.CreateAsync(customerRole);
                    }

                    // Add user to Customer role
                    await _userManager.AddToRoleAsync(user, "Customer");

                    await _signInManager.SignInAsync(user, isPersistent: false);
                    TempData["Message"] = "Registration successful! Welcome to Hotel Booking System.";
                    return RedirectToAction("Index", "Home");
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            await _signInManager.SignOutAsync();
            TempData["Message"] = "You have been logged out successfully.";
            return RedirectToAction("Login");
        }

        public IActionResult AccessDenied()
        {
            return View();
        }

        [HttpGet]
        public IActionResult DemoAccounts()
        {
            var demoAccounts = new List<object>
            {
                new {
                    Role = "Admin",
                    Username = "admin",
                    Email = "<EMAIL>",
                    Password = "Admin123",
                    Description = "Quản trị viên hệ thống - Có quyền truy cập tất cả chức năng",
                    Features = new[] { "Quản lý người dùng", "Quản lý phòng", "Báo cáo", "Cài đặt hệ thống" }
                },
                new {
                    Role = "Super Admin",
                    Username = "superadmin",
                    Email = "<EMAIL>",
                    Password = "123456",
                    Description = "Quản trị viên cấp cao - Tài khoản đơn giản với password 123456",
                    Features = new[] { "Quản lý người dùng", "Quản lý phòng", "Báo cáo", "Cài đặt hệ thống", "Toàn quyền" }
                },
                new {
                    Role = "Staff",
                    Username = "staff",
                    Email = "<EMAIL>",
                    Password = "Staff123",
                    Description = "Nhân viên khách sạn - Quản lý đặt phòng và khách hàng",
                    Features = new[] { "Quản lý đặt phòng", "Xử lý thanh toán", "Quản lý feedback", "Gửi thông báo" }
                },
                new {
                    Role = "Customer",
                    Username = "customer",
                    Email = "<EMAIL>",
                    Password = "Customer123",
                    Description = "Khách hàng - Đặt phòng và quản lý booking",
                    Features = new[] { "Tìm kiếm phòng", "Đặt phòng", "Xem lịch sử", "Đánh giá dịch vụ" }
                }
            };

            return View(demoAccounts);
        }

        [HttpGet]
        public async Task<IActionResult> CheckDatabase()
        {
            var users = new List<object>();

            try
            {
                // Kiểm tra tất cả users trong database
                var allUsers = _userManager.Users.ToList();

                foreach (var user in allUsers)
                {
                    var roles = await _userManager.GetRolesAsync(user);
                    users.Add(new
                    {
                        Id = user.Id,
                        UserName = user.UserName,
                        Email = user.Email,
                        EmailConfirmed = user.EmailConfirmed,
                        IsActive = user.IsActive,
                        RoleID = user.CustomRoleId ?? 0,
                        Roles = string.Join(", ", roles),
                        CreatedDate = user.CreatedDate,
                        CreatedBy = user.CreatedBy
                    });
                }

                ViewBag.Message = $"Tìm thấy {users.Count} users trong database";
            }
            catch (Exception ex)
            {
                ViewBag.Error = $"Lỗi khi truy vấn database: {ex.Message}";
            }

            return View(users);
        }








    }
}
