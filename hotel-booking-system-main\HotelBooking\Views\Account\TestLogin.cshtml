@{
    ViewData["Title"] = "Test Login";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="text-center mb-4">
                <h2 class="text-primary">🧪 Test Login</h2>
                <p class="text-muted">Kiể<PERSON> tra quá trình đăng nhập vớ<PERSON> tà<PERSON> <EMAIL></p>
            </div>

            @if (ViewBag.Error != null)
            {
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <strong>Lỗi:</strong> @ViewBag.Error
                </div>
            }

            @if (ViewBag.Message != null)
            {
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> @ViewBag.Message
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clipboard-check"></i> Kết quả Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Tìm User:</h6>
                                @if (ViewBag.UserFound)
                                {
                                    <span class="badge bg-success fs-6">✓ Tìm thấy user</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger fs-6">✗ Không tìm thấy user</span>
                                }
                            </div>
                            <div class="col-md-6">
                                <h6>Kiểm tra Password:</h6>
                                @if (ViewBag.PasswordValid)
                                {
                                    <span class="badge bg-success fs-6">✓ Password đúng</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger fs-6">✗ Password sai</span>
                                }
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Sign In Succeeded:</h6>
                                @if (ViewBag.SignInSucceeded)
                                {
                                    <span class="badge bg-success fs-6">✓ Thành công</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger fs-6">✗ Thất bại</span>
                                }
                            </div>
                            <div class="col-md-6">
                                <h6>Requires Two Factor:</h6>
                                @if (ViewBag.SignInRequiresTwoFactor)
                                {
                                    <span class="badge bg-warning fs-6">⚠ Cần 2FA</span>
                                }
                                else
                                {
                                    <span class="badge bg-success fs-6">✓ Không cần 2FA</span>
                                }
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Is Locked Out:</h6>
                                @if (ViewBag.SignInIsLockedOut)
                                {
                                    <span class="badge bg-danger fs-6">🔒 Bị khóa</span>
                                }
                                else
                                {
                                    <span class="badge bg-success fs-6">✓ Không bị khóa</span>
                                }
                            </div>
                            <div class="col-md-6">
                                <h6>Is Not Allowed:</h6>
                                @if (ViewBag.SignInIsNotAllowed)
                                {
                                    <span class="badge bg-warning fs-6">⚠ Không được phép</span>
                                }
                                else
                                {
                                    <span class="badge bg-success fs-6">✓ Được phép</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }

            <div class="text-center mt-4">
                <a href="/Account/TestLogin" class="btn btn-primary">
                    <i class="fas fa-redo"></i> Chạy lại test
                </a>
                <a href="/Account/CheckDatabase" class="btn btn-info">
                    <i class="fas fa-database"></i> Kiểm tra Database
                </a>
                <a href="/Account/Login" class="btn btn-outline-secondary">
                    <i class="fas fa-sign-in-alt"></i> Đăng nhập
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.badge.fs-6 {
    font-size: 0.9rem !important;
    padding: 0.5rem 0.75rem;
}
</style>
