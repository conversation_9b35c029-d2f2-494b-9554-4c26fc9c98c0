@model LoginViewModel
@{
    ViewData["Title"] = "Sign In - HotelPro";
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="login-body">
    <div class="login-container">
        <div class="login-card">
            <!-- Logo and Title -->
            <div class="login-header">
                <div class="logo-container">
                    <i class="fas fa-building logo-icon"></i>
                </div>
                <h1 class="brand-title">HotelPro</h1>
                <p class="brand-subtitle">Hotel Management System</p>
            </div>

            <!-- Welcome Section -->
            <div class="welcome-section">
                <h2 class="welcome-title">Welcome</h2>
                <p class="welcome-subtitle">Sign in to your account or create a new one</p>
            </div>

            <!-- Tab Navigation -->
            <div class="tab-navigation">
                <button class="tab-btn active" id="signin-tab">Sign In</button>
                <a href="@Url.Action("Register", "Account")" class="tab-btn" id="signup-tab">Sign Up</a>
            </div>

            <!-- Login Form -->
            <form asp-action="Login" asp-controller="Account" method="post" class="login-form">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input name="ReturnUrl" type="hidden" value="@ViewData["ReturnUrl"]" />

                <div class="form-group">
                    <label asp-for="Email" class="form-label">Email</label>
                    <input asp-for="Email" class="form-control" placeholder="Enter your email" />
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <label asp-for="Password" class="form-label">Password</label>
                    <input asp-for="Password" class="form-control" placeholder="Enter your password" />
                    <span asp-validation-for="Password" class="text-danger"></span>
                </div>

                <button type="submit" class="btn-signin">Sign In</button>
            </form>

            <!-- Demo Accounts -->
            <div class="demo-section">
                <p class="demo-title">Demo Accounts:</p>
                <div class="demo-buttons">
                    <form asp-action="DemoLogin" asp-controller="Account" method="post" style="display: inline;">
                        <input type="hidden" name="role" value="admin" />
                        <button type="submit" class="demo-btn">Admin</button>
                    </form>
                    <form asp-action="DemoLogin" asp-controller="Account" method="post" style="display: inline;">
                        <input type="hidden" name="role" value="staff" />
                        <button type="submit" class="demo-btn">Staff</button>
                    </form>
                    <form asp-action="DemoLogin" asp-controller="Account" method="post" style="display: inline;">
                        <input type="hidden" name="role" value="customer" />
                        <button type="submit" class="demo-btn">Customer</button>
                    </form>
                </div>
                <div class="text-center mt-3">
                    <a href="@Url.Action("DemoAccounts", "Account")" class="text-decoration-none">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>Xem chi tiết tài khoản demo
                        </small>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/login.js" asp-append-version="true"></script>
</body>
</html>
