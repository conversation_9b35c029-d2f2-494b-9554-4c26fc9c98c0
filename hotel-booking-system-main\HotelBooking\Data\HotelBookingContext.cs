using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using HotelBooking.Models;

namespace HotelBooking.Data
{
    public class HotelBookingContext : IdentityDbContext<CustomUser, CustomRole, int>
    {
        public HotelBookingContext(DbContextOptions<HotelBookingContext> options) : base(options)
        {
        }

        // Identity tables are inherited from IdentityDbContext
        public DbSet<Room> Rooms { get; set; }
        public DbSet<RoomType> RoomTypes { get; set; }
        public DbSet<Amenity> Amenities { get; set; }
        public DbSet<RoomAmenity> RoomAmenities { get; set; }
        public DbSet<Reservation> Reservations { get; set; }
        public DbSet<ReservationGuest> ReservationGuests { get; set; }
        public DbSet<Guest> Guests { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<PaymentBatch> PaymentBatches { get; set; }
        public DbSet<Refund> Refunds { get; set; }
        public DbSet<RefundMethod> RefundMethods { get; set; }
        public DbSet<Cancellation> Cancellations { get; set; }
        public DbSet<Feedback> Feedbacks { get; set; }
        public DbSet<Country> Countries { get; set; }
        public DbSet<State> States { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<RoomChangeHistory> RoomChangeHistories { get; set; }
        public DbSet<Service> Services { get; set; }
        public DbSet<BookingServiceUsage> BookingServiceUsages { get; set; }
        public DbSet<LoyaltyPoint> LoyaltyPoints { get; set; }
        public DbSet<LoyaltyTier> LoyaltyTiers { get; set; }
        public DbSet<CustomerLoyalty> CustomerLoyalties { get; set; }
        public DbSet<ServiceInventory> ServiceInventories { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }
        public DbSet<ServiceAvailability> ServiceAvailabilities { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Minimal configuration - just composite keys
            modelBuilder.Entity<RoomAmenity>()
                .HasKey(ra => new { ra.RoomID, ra.AmenityID });

            modelBuilder.Entity<ReservationGuest>()
                .HasKey(rg => new { rg.ReservationID, rg.GuestID });

            modelBuilder.Entity<RoomAmenity>()
                .HasKey(ra => new { ra.RoomID, ra.AmenityID });

            modelBuilder.Entity<RoomAmenity>()
                .HasOne(ra => ra.Room)
                .WithMany(r => r.RoomAmenities)
                .HasForeignKey(ra => ra.RoomID);

            modelBuilder.Entity<RoomAmenity>()
                .HasOne(ra => ra.Amenity)
                .WithMany(a => a.RoomAmenities)
                .HasForeignKey(ra => ra.AmenityID);

            modelBuilder.Entity<ReservationGuest>()
                .HasKey(rg => new { rg.ReservationID, rg.GuestID });

            modelBuilder.Entity<ReservationGuest>()
                .HasOne(rg => rg.Reservation)
                .WithMany(r => r.ReservationGuests)
                .HasForeignKey(rg => rg.ReservationID);

            modelBuilder.Entity<ReservationGuest>()
                .HasOne(rg => rg.Guest)
                .WithMany(g => g.ReservationGuests)
                .HasForeignKey(rg => rg.GuestID);

            // Configure Room relationships
            modelBuilder.Entity<Room>()
                .HasOne(r => r.RoomType)
                .WithMany(rt => rt.Rooms)
                .HasForeignKey(r => r.RoomTypeID);

            // Configure Reservation relationships
            modelBuilder.Entity<Reservation>()
                .HasOne(r => r.User)
                .WithMany(u => u.Reservations)
                .HasForeignKey(r => r.UserID);

            modelBuilder.Entity<Reservation>()
                .HasOne(r => r.Room)
                .WithMany(rm => rm.Reservations)
                .HasForeignKey(r => r.RoomID);

            // Configure Payment relationships
            modelBuilder.Entity<Payment>()
                .HasOne(p => p.Reservation)
                .WithMany(r => r.Payments)
                .HasForeignKey(p => p.ReservationID)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PaymentBatch>()
                .HasOne(pb => pb.User)
                .WithMany()
                .HasForeignKey(pb => pb.UserID)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure State relationships
            modelBuilder.Entity<State>()
                .HasOne(s => s.Country)
                .WithMany(c => c.States)
                .HasForeignKey(s => s.CountryID)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Guest relationships to avoid cascade conflicts
            modelBuilder.Entity<Guest>()
                .HasOne(g => g.User)
                .WithMany()
                .HasForeignKey(g => g.UserID)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Guest>()
                .HasOne(g => g.Country)
                .WithMany()
                .HasForeignKey(g => g.CountryID)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Guest>()
                .HasOne(g => g.State)
                .WithMany()
                .HasForeignKey(g => g.StateID)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Notification relationships
            modelBuilder.Entity<Notification>()
                .HasOne(n => n.User)
                .WithMany()
                .HasForeignKey(n => n.UserID)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure RoomChangeHistory relationships
            modelBuilder.Entity<RoomChangeHistory>()
                .HasOne(rch => rch.Reservation)
                .WithMany()
                .HasForeignKey(rch => rch.ReservationID)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<RoomChangeHistory>()
                .HasOne(rch => rch.OldRoom)
                .WithMany()
                .HasForeignKey(rch => rch.OldRoomID)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<RoomChangeHistory>()
                .HasOne(rch => rch.NewRoom)
                .WithMany()
                .HasForeignKey(rch => rch.NewRoomID)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure BookingServiceUsage relationships
            modelBuilder.Entity<BookingServiceUsage>()
                .HasOne(bsu => bsu.Reservation)
                .WithMany()
                .HasForeignKey(bsu => bsu.ReservationID)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<BookingServiceUsage>()
                .HasOne(bsu => bsu.Service)
                .WithMany(s => s.BookingServiceUsages)
                .HasForeignKey(bsu => bsu.ServiceID)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Loyalty relationships
            modelBuilder.Entity<LoyaltyPoint>()
                .HasOne(lp => lp.User)
                .WithMany()
                .HasForeignKey(lp => lp.UserID)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<CustomerLoyalty>()
                .HasOne(cl => cl.User)
                .WithMany()
                .HasForeignKey(cl => cl.UserID)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<CustomerLoyalty>()
                .HasOne(cl => cl.LoyaltyTier)
                .WithMany(lt => lt.CustomerLoyalties)
                .HasForeignKey(cl => cl.LoyaltyTierID)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Service Inventory relationships
            modelBuilder.Entity<ServiceInventory>()
                .HasOne(si => si.Service)
                .WithMany()
                .HasForeignKey(si => si.ServiceID)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InventoryTransaction>()
                .HasOne(it => it.ServiceInventory)
                .WithMany(si => si.InventoryTransactions)
                .HasForeignKey(it => it.ServiceInventoryID)
                .OnDelete(DeleteBehavior.Restrict);

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed Countries
            modelBuilder.Entity<Country>().HasData(
                new Country { CountryID = 1, CountryName = "Vietnam", CountryCode = "VN", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Country { CountryID = 2, CountryName = "United States", CountryCode = "US", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now }
            );

            // Seed States
            modelBuilder.Entity<State>().HasData(
                new State { StateID = 1, StateName = "Ho Chi Minh City", StateCode = "HCM", CountryID = 1, IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new State { StateID = 2, StateName = "Hanoi", StateCode = "HN", CountryID = 1, IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now }
            );

            // Seed Roles
            modelBuilder.Entity<CustomRole>().HasData(
                new CustomRole { Id = 1, Name = "Admin", NormalizedName = "ADMIN", RoleName = "Admin", Description = "Administrator", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new CustomRole { Id = 2, Name = "Customer", NormalizedName = "CUSTOMER", RoleName = "Customer", Description = "Customer", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new CustomRole { Id = 3, Name = "Staff", NormalizedName = "STAFF", RoleName = "Staff", Description = "Hotel Staff", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now }
            );

            // Seed Room Types
            modelBuilder.Entity<RoomType>().HasData(
                new RoomType { RoomTypeID = 1, TypeName = "Standard", Description = "Standard Room", MaxOccupancy = 2, IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new RoomType { RoomTypeID = 2, TypeName = "Deluxe", Description = "Deluxe Room", MaxOccupancy = 3, IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new RoomType { RoomTypeID = 3, TypeName = "Suite", Description = "Suite Room", MaxOccupancy = 4, IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now }
            );

            // Seed Amenities
            modelBuilder.Entity<Amenity>().HasData(
                new Amenity { AmenityID = 1, AmenityName = "WiFi", Description = "Free WiFi", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Amenity { AmenityID = 2, AmenityName = "Air Conditioning", Description = "Air Conditioning", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Amenity { AmenityID = 3, AmenityName = "TV", Description = "Television", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Amenity { AmenityID = 4, AmenityName = "Mini Bar", Description = "Mini Bar", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now }
            );

            // Seed Rooms
            modelBuilder.Entity<Room>().HasData(
                new Room { RoomID = 1, RoomNumber = "101", RoomTypeID = 1, Price = 100.00m, BedType = "Single", ViewType = "City", Status = "Available", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Room { RoomID = 2, RoomNumber = "102", RoomTypeID = 1, Price = 100.00m, BedType = "Double", ViewType = "City", Status = "Available", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Room { RoomID = 3, RoomNumber = "201", RoomTypeID = 2, Price = 150.00m, BedType = "Queen", ViewType = "Ocean", Status = "Available", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Room { RoomID = 4, RoomNumber = "301", RoomTypeID = 3, Price = 250.00m, BedType = "King", ViewType = "Ocean", Status = "Available", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now }
            );

            // Seed Services
            modelBuilder.Entity<Service>().HasData(
                new Service { ServiceID = 1, ServiceName = "Room Service - Breakfast", Description = "Continental breakfast delivered to room", Category = "Food", UnitPrice = 25.00m, Unit = "per meal", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Service { ServiceID = 2, ServiceName = "Room Service - Lunch", Description = "Lunch menu delivered to room", Category = "Food", UnitPrice = 35.00m, Unit = "per meal", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Service { ServiceID = 3, ServiceName = "Room Service - Dinner", Description = "Dinner menu delivered to room", Category = "Food", UnitPrice = 45.00m, Unit = "per meal", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Service { ServiceID = 4, ServiceName = "Bottled Water", Description = "Premium bottled water", Category = "Beverage", UnitPrice = 5.00m, Unit = "per bottle", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Service { ServiceID = 5, ServiceName = "Soft Drinks", Description = "Assorted soft drinks", Category = "Beverage", UnitPrice = 8.00m, Unit = "per can", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Service { ServiceID = 6, ServiceName = "Laundry Service", Description = "Professional laundry and dry cleaning", Category = "Laundry", UnitPrice = 15.00m, Unit = "per item", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Service { ServiceID = 7, ServiceName = "Spa Massage", Description = "60-minute relaxing massage", Category = "Spa", UnitPrice = 80.00m, Unit = "per session", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new Service { ServiceID = 8, ServiceName = "Airport Transfer", Description = "Private car to/from airport", Category = "Transportation", UnitPrice = 50.00m, Unit = "per trip", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now }
            );

            // Seed Loyalty Tiers
            modelBuilder.Entity<LoyaltyTier>().HasData(
                new LoyaltyTier { LoyaltyTierID = 1, TierName = "Bronze", MinPoints = 0, MaxPoints = 999, DiscountPercentage = 0, PointMultiplier = 1.0m, Benefits = "Welcome bonus, Basic support", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new LoyaltyTier { LoyaltyTierID = 2, TierName = "Silver", MinPoints = 1000, MaxPoints = 4999, DiscountPercentage = 5, PointMultiplier = 1.2m, Benefits = "5% discount, Priority support, Late checkout", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new LoyaltyTier { LoyaltyTierID = 3, TierName = "Gold", MinPoints = 5000, MaxPoints = 14999, DiscountPercentage = 10, PointMultiplier = 1.5m, Benefits = "10% discount, Room upgrade, Free breakfast", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now },
                new LoyaltyTier { LoyaltyTierID = 4, TierName = "Platinum", MinPoints = 15000, MaxPoints = 999999, DiscountPercentage = 15, PointMultiplier = 2.0m, Benefits = "15% discount, Suite upgrade, Concierge service, Airport transfer", IsActive = true, CreatedBy = "System", CreatedDate = DateTime.Now }
            );

            // Seed Service Inventory
            modelBuilder.Entity<ServiceInventory>().HasData(
                new ServiceInventory { ServiceInventoryID = 1, ServiceID = 4, ItemName = "Premium Water Bottles", CurrentStock = 100, MinimumStock = 20, MaximumStock = 200, ReorderLevel = 30, CostPerUnit = 2.50m, Unit = "bottles", Supplier = "AquaPure Co.", Status = "In Stock", CreatedBy = "System", CreatedDate = DateTime.Now },
                new ServiceInventory { ServiceInventoryID = 2, ServiceID = 5, ItemName = "Assorted Soft Drinks", CurrentStock = 80, MinimumStock = 15, MaximumStock = 150, ReorderLevel = 25, CostPerUnit = 3.00m, Unit = "cans", Supplier = "Beverage Plus", Status = "In Stock", CreatedBy = "System", CreatedDate = DateTime.Now },
                new ServiceInventory { ServiceInventoryID = 3, ServiceID = 6, ItemName = "Laundry Supplies", CurrentStock = 50, MinimumStock = 10, MaximumStock = 100, ReorderLevel = 15, CostPerUnit = 5.00m, Unit = "sets", Supplier = "CleanCorp", Status = "In Stock", CreatedBy = "System", CreatedDate = DateTime.Now }
            );
        }
    }
}
