@model IEnumerable<HotelBooking.Models.Reservation>
@{
    ViewData["Title"] = "Reservations Management";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="admin-content">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h2 mb-1 fw-bold">Reservations</h1>
            <p class="text-muted mb-0">Manage guest bookings and reservations across all properties.</p>
        </div>
        <button class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>New Reservation
        </button>
    </div>

    <!-- Search and Filter Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <form method="get" class="d-flex">
                <div class="input-group input-group-lg">
                    <span class="input-group-text bg-white border-end-0">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" class="form-control border-start-0 ps-0"
                           name="search" value="@ViewBag.Search"
                           placeholder="Search by guest name, email, or hotel...">
                </div>
                <div class="col-md-4">
                    <select name="status" class="form-select form-select-lg" onchange="this.form.submit()">
                        <option value="">All Statuses</option>
                        <option value="Confirmed" selected="@(ViewBag.Status == "Confirmed")">Confirmed</option>
                        <option value="Pending" selected="@(ViewBag.Status == "Pending")">Pending</option>
                        <option value="Cancelled" selected="@(ViewBag.Status == "Cancelled")">Cancelled</option>
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="text-muted mb-1">Total Reservations</h6>
                            <h3 class="mb-0 fw-bold">@ViewBag.TotalReservations</h3>
                            <small class="text-muted">All time</small>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="text-muted mb-1">Confirmed</h6>
                            <h3 class="mb-0 fw-bold text-success">@ViewBag.ConfirmedReservations</h3>
                            <small class="text-muted">Active bookings</small>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="text-muted mb-1">Pending</h6>
                            <h3 class="mb-0 fw-bold text-warning">@ViewBag.PendingReservations</h3>
                            <small class="text-muted">Awaiting confirmation</small>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="text-muted mb-1">Revenue</h6>
                            <h3 class="mb-0 fw-bold text-info">$@ViewBag.TotalRevenue</h3>
                            <small class="text-muted">Total bookings value</small>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservations List -->
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            @if (Model.Any())
            {
                @foreach (var reservation in Model)
                {
                    var nights = (reservation.CheckOutDate - reservation.CheckInDate).Days;
                    var totalAmount = reservation.Room?.Price * nights ?? 0;

                    <div class="reservation-item p-4 border-bottom">
                        <div class="row align-items-center">
                            <div class="col-md-1">
                                <div class="user-avatar">
                                    <i class="fas fa-user-circle fa-3x text-primary"></i>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <h6 class="mb-1 fw-bold">@reservation.User?.UserName</h6>
                                <small class="text-muted">@reservation.User?.Email</small>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <strong>@reservation.Room?.RoomNumber - @reservation.Room?.RoomType?.TypeName</strong>
                                    <div class="small text-muted">@reservation.Room?.RoomType?.TypeName</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <strong>@reservation.CheckInDate.ToString("dd/MM/yyyy")</strong>
                                    <div class="small text-muted">Check-in</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <strong>$@totalAmount.ToString("F2")</strong>
                                    <div class="small text-muted">@nights night@(nights > 1 ? "s" : "")</div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                @if (reservation.Status == "Confirmed")
                                {
                                    <span class="badge bg-success fs-6 px-3 py-2">
                                        <i class="fas fa-check me-1"></i>Confirmed
                                    </span>
                                }
                                else if (reservation.Status == "Pending")
                                {
                                    <span class="badge bg-warning fs-6 px-3 py-2">
                                        <i class="fas fa-clock me-1"></i>Pending
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary fs-6 px-3 py-2">@reservation.Status</span>
                                }
                            </div>
                            <div class="col-md-1">
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-h"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>View</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>Edit</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Delete</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No reservations found</h5>
                    <p class="text-muted">Try adjusting your search criteria or create a new reservation.</p>
                </div>
            }
        </div>
    </div>
</div>

<style>
.admin-content {
    padding: 2rem;
    background-color: #f8f9fa;
    min-height: 100vh;
}

.reservation-item:hover {
    background-color: #f8f9fa;
}

.reservation-item:last-child {
    border-bottom: none !important;
}

.user-avatar {
    text-align: center;
}

.card {
    border-radius: 12px;
}

.input-group-text {
    border-radius: 8px 0 0 8px;
}

.form-control, .form-select {
    border-radius: 0 8px 8px 0;
}

.badge {
    border-radius: 8px;
}
</style>
