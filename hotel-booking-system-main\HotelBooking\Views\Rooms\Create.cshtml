@model HotelBooking.Models.ViewModels.RoomViewModel
@{
    ViewData["Title"] = "Create Room";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bed"></i> Create New Room
                    </h3>
                </div>

                <form asp-action="Create" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="RoomNumber" class="control-label"></label>
                                    <input asp-for="RoomNumber" class="form-control" />
                                    <span asp-validation-for="RoomNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="RoomTypeID" class="control-label"></label>
                                    <select asp-for="RoomTypeID" class="form-control">
                                        <option value="">Select Room Type</option>
                                        @foreach (var roomType in Model.RoomTypes)
                                        {
                                            <option value="@roomType.RoomTypeID">@roomType.TypeName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="RoomTypeID" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Price" class="control-label"></label>
                                    <input asp-for="Price" class="form-control" type="number" step="0.01" />
                                    <span asp-validation-for="Price" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Status" class="control-label"></label>
                                    <select asp-for="Status" class="form-control">
                                        @foreach (var status in Model.StatusOptions)
                                        {
                                            <option value="@status">@status</option>
                                        }
                                    </select>
                                    <span asp-validation-for="Status" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="Description" class="control-label"></label>
                            <textarea asp-for="Description" class="form-control" rows="4"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label class="control-label">Amenities</label>
                            <div class="row">
                                @foreach (var amenity in Model.Amenities)
                                {
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input type="checkbox" name="SelectedAmenityIds" value="@amenity.AmenityID" 
                                                   class="form-check-input" id="<EMAIL>" />
                                            <label class="form-check-label" for="<EMAIL>">
                                                @if (!string.IsNullOrEmpty(amenity.Icon))
                                                {
                                                    <i class="@amenity.Icon"></i>
                                                }
                                                @amenity.AmenityName
                                            </label>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                <label asp-for="IsActive" class="form-check-label"></label>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create Room
                        </button>
                        <a href="@Url.Action("Index")" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
