@model HotelBooking.Models.ViewModels.AddServiceViewModel
@{
    ViewData["Title"] = "Add Service";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Add Service
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Reservation Info -->
                    <div class="alert alert-info mb-4">
                        <h6><i class="fas fa-info-circle me-2"></i>Reservation Details</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Room:</strong> @Model.RoomNumber
                            </div>
                            <div class="col-md-4">
                                <strong>Check-in:</strong> @Model.CheckInDate.ToString("MMM dd, yyyy")
                            </div>
                            <div class="col-md-4">
                                <strong>Check-out:</strong> @Model.CheckOutDate.ToString("MMM dd, yyyy")
                            </div>
                        </div>
                    </div>

                    <!-- Add Service Form -->
                    <form asp-action="AddService" method="post">
                        <input type="hidden" asp-for="ReservationID" />

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label asp-for="ServiceID" class="form-label fw-bold">Select Service:</label>
                                    <select asp-for="ServiceID" class="form-select" id="serviceSelect">
                                        <option value="">-- Select a service --</option>
                                        @if (Model.AvailableServices != null)
                                        {
                                            var groupedServices = Model.AvailableServices.GroupBy(s => s.Category);
                                            @foreach (var group in groupedServices)
                                            {
                                                <optgroup label="@group.Key">
                                                    @foreach (var service in group)
                                                    {
                                                        <option value="@service.ServiceID" 
                                                                data-price="@service.UnitPrice" 
                                                                data-unit="@service.Unit"
                                                                data-description="@service.Description">
                                                            @service.ServiceName - $@service.UnitPrice @service.Unit
                                                        </option>
                                                    }
                                                </optgroup>
                                            }
                                        }
                                    </select>
                                    <span asp-validation-for="ServiceID" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Quantity" class="form-label fw-bold">Quantity:</label>
                                    <input asp-for="Quantity" class="form-control" type="number" min="1" max="10" value="1" id="quantityInput" />
                                    <span asp-validation-for="Quantity" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Service Details Display -->
                        <div class="card bg-light mb-3" id="serviceDetails" style="display: none;">
                            <div class="card-body">
                                <h6 class="card-title">Service Details</h6>
                                <p class="mb-2" id="serviceDescription"></p>
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>Unit Price:</strong> $<span id="unitPrice">0</span> <span id="unitType"></span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Quantity:</strong> <span id="selectedQuantity">1</span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Total Cost:</strong> $<span id="totalCost" class="text-success fw-bold">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Note" class="form-label fw-bold">Special Notes (Optional):</label>
                            <textarea asp-for="Note" class="form-control" rows="3" placeholder="Any special instructions or preferences..."></textarea>
                            <span asp-validation-for="Note" class="text-danger"></span>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-success btn-lg" id="addServiceBtn" disabled>
                                <i class="fas fa-plus-circle me-2"></i>Add Service
                            </button>
                            <a href="@Url.Action("Details", new { id = Model.ReservationID })" class="btn btn-secondary btn-lg ms-2">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Available Services Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-concierge-bell me-2"></i>Available Services
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.AvailableServices != null)
                    {
                        var servicesByCategory = Model.AvailableServices.GroupBy(s => s.Category);
                        <div class="row">
                            @foreach (var category in servicesByCategory)
                            {
                                <div class="col-md-6 mb-3">
                                    <h6 class="text-primary">@category.Key</h6>
                                    <ul class="list-unstyled">
                                        @foreach (var service in category)
                                        {
                                            <li class="mb-2">
                                                <strong>@service.ServiceName</strong> - $@service.UnitPrice @service.Unit
                                                <br><small class="text-muted">@service.Description</small>
                                            </li>
                                        }
                                    </ul>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const serviceSelect = document.getElementById('serviceSelect');
        const quantityInput = document.getElementById('quantityInput');
        const serviceDetails = document.getElementById('serviceDetails');
        const addServiceBtn = document.getElementById('addServiceBtn');

        function updateServiceDetails() {
            const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
            const quantity = parseInt(quantityInput.value) || 1;
            
            if (selectedOption.value) {
                const unitPrice = parseFloat(selectedOption.dataset.price);
                const unitType = selectedOption.dataset.unit;
                const description = selectedOption.dataset.description;
                const totalCost = unitPrice * quantity;
                
                document.getElementById('serviceDescription').textContent = description;
                document.getElementById('unitPrice').textContent = unitPrice.toFixed(2);
                document.getElementById('unitType').textContent = unitType;
                document.getElementById('selectedQuantity').textContent = quantity;
                document.getElementById('totalCost').textContent = totalCost.toFixed(2);
                
                serviceDetails.style.display = 'block';
                addServiceBtn.disabled = false;
            } else {
                serviceDetails.style.display = 'none';
                addServiceBtn.disabled = true;
            }
        }

        serviceSelect.addEventListener('change', updateServiceDetails);
        quantityInput.addEventListener('input', updateServiceDetails);
    });
</script>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
