using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using HotelBooking.Models;
using HotelBooking.Data;
using Microsoft.EntityFrameworkCore;

namespace HotelBooking.Controllers
{
    [Authorize(Roles = "Customer")]
    public class CustomerController : Controller
    {
        private readonly HotelBookingContext _context;
        private readonly UserManager<CustomUser> _userManager;

        public CustomerController(HotelBookingContext context, UserManager<CustomUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        // GET: Customer/Profile
        public async Task<IActionResult> Profile()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound();
            }

            var guest = await _context.Guests
                .Include(g => g.Country)
                .Include(g => g.State)
                .FirstOrDefaultAsync(g => g.UserID == user.Id);

            if (guest == null)
            {
                // Create a new guest profile if it doesn't exist
                ViewBag.Message = "Please complete your profile information.";
                return View("CreateProfile");
            }

            return View(guest);
        }

        // GET: Customer/Feedback
        public IActionResult Feedback()
        {
            ViewBag.Message = "Share your experience with us!";
            return View();
        }

        // POST: Customer/Feedback
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Feedback(string comment, int rating)
        {
            if (string.IsNullOrEmpty(comment) || rating < 1 || rating > 5)
            {
                ModelState.AddModelError("", "Please provide a valid comment and rating (1-5 stars).");
                return View();
            }

            var user = await _userManager.GetUserAsync(User);
            var guest = await _context.Guests.FirstOrDefaultAsync(g => g.UserID == user!.Id);

            if (guest != null)
            {
                var feedback = new Feedback
                {
                    GuestID = guest.GuestID,
                    Comment = comment,
                    Rating = rating,
                    FeedbackDate = DateTime.Now,
                    CreatedBy = User.Identity?.Name ?? "Customer",
                    CreatedDate = DateTime.Now
                };

                _context.Feedbacks.Add(feedback);
                await _context.SaveChangesAsync();

                TempData["Message"] = "Thank you for your feedback!";
                return RedirectToAction("Feedback");
            }

            ModelState.AddModelError("", "Unable to submit feedback. Please try again.");
            return View();
        }

        // GET: Customer/Notifications
        public IActionResult Notifications()
        {
            // For now, show a simple notifications page
            ViewBag.Message = "Stay updated with your booking notifications!";
            return View();
        }
    }
}
