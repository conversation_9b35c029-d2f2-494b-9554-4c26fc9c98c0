@model HomeViewModel
@{
    ViewData["Title"] = "Discover Amazing Hotels";
}

<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .hero-banner {
        background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                    url('https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2080&q=80');
        background-size: cover;
        background-position: center;
        height: 70vh;
        display: flex;
        align-items: center;
        color: white;
        position: relative;
        margin-bottom: 4rem;
    }

    .hero-content {
        text-align: center;
        z-index: 2;
    }

    .hero-title {
        font-size: 4rem;
        font-weight: 800;
        margin-bottom: 1.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        animation: fadeInUp 1s ease-out;
    }

    .hero-subtitle {
        font-size: 1.5rem;
        margin-bottom: 2rem;
        opacity: 0.95;
        animation: fadeInUp 1s ease-out 0.3s both;
    }

    .search-box {
        background: rgba(255,255,255,0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        animation: fadeInUp 1s ease-out 0.6s both;
    }

    .destination-card {
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        margin-bottom: 2rem;
        position: relative;
    }

    .destination-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .destination-image {
        height: 250px;
        background-size: cover;
        background-position: center;
        position: relative;
    }

    .destination-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.7));
        display: flex;
        align-items: flex-end;
        padding: 1.5rem;
    }

    .destination-info {
        color: white;
        z-index: 2;
    }

    .destination-name {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .destination-location {
        opacity: 0.9;
        margin-bottom: 1rem;
    }

    .price-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--primary-gradient);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .room-type-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }

    .room-type-card:hover {
        transform: translateX(5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        border-left-color: #667eea;
    }

    .stats-section {
        background: var(--primary-gradient);
        color: white;
        padding: 4rem 0;
        margin: 4rem 0;
    }

    .stat-item {
        text-align: center;
        margin-bottom: 2rem;
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 800;
        display: block;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .btn-explore {
        background: var(--secondary-gradient);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .btn-explore:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        color: white;
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 3rem;
        color: #2c3e50;
    }

    .floating-elements {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        pointer-events: none;
    }

    .floating-elements::before,
    .floating-elements::after {
        content: '';
        position: absolute;
        width: 200px;
        height: 200px;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }

    .floating-elements::before {
        top: 10%;
        left: 10%;
        animation-delay: 0s;
    }

    .floating-elements::after {
        bottom: 10%;
        right: 10%;
        animation-delay: 3s;
    }

    @@keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
</style>

<!-- Hero Banner -->
<div class="hero-banner">
    <div class="floating-elements"></div>
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">Discover Amazing Hotels</h1>
            <p class="hero-subtitle">Experience luxury and comfort in the world's most beautiful destinations</p>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="search-box">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control form-control-lg" placeholder="Where to?">
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control form-control-lg">
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control form-control-lg">
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-explore w-100">Search</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Featured Destinations -->
<div class="container">
    <h2 class="section-title">Featured Destinations</h2>
    <div class="row">
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="destination-card">
                <div class="destination-image" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                    <div class="price-badge">From $299/night</div>
                    <div class="destination-overlay">
                        <div class="destination-info">
                            <h3 class="destination-name">Santorini Paradise</h3>
                            <p class="destination-location"><i class="fas fa-map-marker-alt"></i> Santorini, Greece</p>
                            <a href="@Url.Action("Index", "Rooms")" class="btn btn-explore">Explore Rooms</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="destination-card">
                <div class="destination-image" style="background-image: url('https://images.unsplash.com/photo-1520637836862-4d197d17c93a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                    <div class="price-badge">From $199/night</div>
                    <div class="destination-overlay">
                        <div class="destination-info">
                            <h3 class="destination-name">Bali Retreat</h3>
                            <p class="destination-location"><i class="fas fa-map-marker-alt"></i> Ubud, Bali</p>
                            <a href="@Url.Action("Index", "Rooms")" class="btn btn-explore">Explore Rooms</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="destination-card">
                <div class="destination-image" style="background-image: url('https://images.unsplash.com/photo-1549294413-26f195200c16?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                    <div class="price-badge">From $399/night</div>
                    <div class="destination-overlay">
                        <div class="destination-info">
                            <h3 class="destination-name">Swiss Alps Lodge</h3>
                            <p class="destination-location"><i class="fas fa-map-marker-alt"></i> Zermatt, Switzerland</p>
                            <a href="@Url.Action("Index", "Rooms")" class="btn btn-explore">Explore Rooms</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="destination-card">
                <div class="destination-image" style="background-image: url('https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                    <div class="price-badge">From $159/night</div>
                    <div class="destination-overlay">
                        <div class="destination-info">
                            <h3 class="destination-name">Tokyo Modern</h3>
                            <p class="destination-location"><i class="fas fa-map-marker-alt"></i> Shibuya, Tokyo</p>
                            <a href="@Url.Action("Index", "Rooms")" class="btn btn-explore">Explore Rooms</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="destination-card">
                <div class="destination-image" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                    <div class="price-badge">From $249/night</div>
                    <div class="destination-overlay">
                        <div class="destination-info">
                            <h3 class="destination-name">Maldives Resort</h3>
                            <p class="destination-location"><i class="fas fa-map-marker-alt"></i> Malé, Maldives</p>
                            <a href="@Url.Action("Index", "Rooms")" class="btn btn-explore">Explore Rooms</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="destination-card">
                <div class="destination-image" style="background-image: url('https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                    <div class="price-badge">From $179/night</div>
                    <div class="destination-overlay">
                        <div class="destination-info">
                            <h3 class="destination-name">Paris Boutique</h3>
                            <p class="destination-location"><i class="fas fa-map-marker-alt"></i> Montmartre, Paris</p>
                            <a href="@Url.Action("Index", "Rooms")" class="btn btn-explore">Explore Rooms</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="stats-section">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">500+</span>
                    <div class="stat-label">Luxury Hotels</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">50K+</span>
                    <div class="stat-label">Happy Guests</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">100+</span>
                    <div class="stat-label">Destinations</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <div class="stat-label">Customer Support</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Room Types & Featured Rooms -->
<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <h2 class="section-title">Featured Rooms</h2>
            <div class="row">
                @if (Model.FeaturedRooms?.Any() == true)
                {
                    @foreach (var room in Model.FeaturedRooms)
                    {
                        <div class="col-md-6 mb-4">
                            <div class="destination-card">
                                <div class="destination-image" style="background-image: url('https://images.unsplash.com/photo-1566665797739-1674de7a421a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                                    <div class="price-badge">$@room.Price/night</div>
                                    <div class="destination-overlay">
                                        <div class="destination-info">
                                            <h3 class="destination-name">Room @room.RoomNumber</h3>
                                            <p class="destination-location"><i class="fas fa-bed"></i> @room.RoomType?.TypeName</p>
                                            <a href="@Url.Action("Details", "Rooms", new { id = room.RoomID })" class="btn btn-explore">View Details</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-bed fa-3x text-muted mb-3"></i>
                            <h4>No featured rooms available</h4>
                            <p class="text-muted">Check back later for amazing room deals!</p>
                        </div>
                    </div>
                }
            </div>
        </div>

        <div class="col-lg-4">
            <h2 class="section-title">Room Types</h2>
            @if (Model.RoomTypes?.Any() == true)
            {
                @foreach (var roomType in Model.RoomTypes)
                {
                    <div class="room-type-card">
                        <a href="@Url.Action("Index", "Rooms", new { roomTypeId = roomType.RoomTypeID })" class="text-decoration-none">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1 text-dark">@roomType.TypeName</h5>
                                    <p class="mb-1 text-muted">@roomType.Description</p>
                                    <small class="text-primary"><i class="fas fa-users"></i> Up to @roomType.MaxOccupancy guests</small>
                                </div>
                                <div>
                                    <i class="fas fa-arrow-right text-primary"></i>
                                </div>
                            </div>
                        </a>
                    </div>
                }
            }
            else
            {
                <div class="text-center py-4">
                    <i class="fas fa-home fa-2x text-muted mb-3"></i>
                    <p class="text-muted">No room types available.</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Call to Action -->
<div class="container text-center my-5">
    <div class="py-5">
        <h2 class="display-5 mb-4">Ready for Your Next Adventure?</h2>
        <p class="lead mb-4">Discover amazing hotels and create unforgettable memories</p>
        <a href="@Url.Action("Index", "Rooms")" class="btn btn-explore btn-lg me-3">Browse All Rooms</a>
        <a href="@Url.Action("Index", "Reservations")" class="btn btn-outline-primary btn-lg">My Bookings</a>
    </div>
</div>