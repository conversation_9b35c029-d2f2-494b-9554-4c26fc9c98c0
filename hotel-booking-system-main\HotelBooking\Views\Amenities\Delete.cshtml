@model HotelBooking.Models.Amenity
@{
    ViewData["Title"] = "Delete Amenity";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle"></i> Delete Amenity
                    </h3>
                </div>

                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> Warning!</h5>
                        Are you sure you want to delete this amenity? This action cannot be undone.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Name:</dt>
                                <dd class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.Icon))
                                    {
                                        <i class="@Model.Icon"></i>
                                    }
                                    @Model.AmenityName
                                </dd>

                                <dt class="col-sm-4">Category:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge badge-secondary">@Model.Category</span>
                                </dd>

                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge badge-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-danger">Inactive</span>
                                    }
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Description:</dt>
                                <dd class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.Description))
                                    {
                                        @Model.Description
                                    }
                                    else
                                    {
                                        <span class="text-muted">No description</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">Created Date:</dt>
                                <dd class="col-sm-8">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</dd>

                                <dt class="col-sm-4">Created By:</dt>
                                <dd class="col-sm-8">@(Model.CreatedBy ?? "System")</dd>
                            </dl>
                        </div>
                    </div>

                    @if (Model.RoomAmenities != null && Model.RoomAmenities.Any())
                    {
                        <hr />
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> Impact Assessment</h6>
                            <p>This amenity is currently assigned to <strong>@Model.RoomAmenities.Count</strong> room(s):</p>
                            <ul class="mb-0">
                                @foreach (var roomAmenity in Model.RoomAmenities.Take(5))
                                {
                                    <li>Room @roomAmenity.Room?.RoomNumber (@roomAmenity.Room?.RoomType?.TypeName)</li>
                                }
                                @if (Model.RoomAmenities.Count > 5)
                                {
                                    <li><em>... and @(Model.RoomAmenities.Count - 5) more room(s)</em></li>
                                }
                            </ul>
                            <p class="mt-2 mb-0">
                                <strong>Note:</strong> Deleting this amenity will remove it from all assigned rooms.
                            </p>
                        </div>
                    }
                    else
                    {
                        <hr />
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            This amenity is not assigned to any rooms, so it can be safely deleted.
                        </div>
                    }
                </div>

                <div class="card-footer">
                    <form asp-action="Delete" method="post" class="d-inline">
                        <input type="hidden" asp-for="AmenityID" />
                        <button type="submit" class="btn btn-danger" 
                                onclick="return confirm('Are you absolutely sure you want to delete this amenity? This action cannot be undone.')">
                            <i class="fas fa-trash"></i> Yes, Delete Amenity
                        </button>
                    </form>
                    <a href="@Url.Action("Details", new { id = Model.AmenityID })" class="btn btn-info">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Cancel & Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
