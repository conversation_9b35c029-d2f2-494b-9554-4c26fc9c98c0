@model HotelBooking.Models.ViewModels.ReportViewModel
@{
    ViewData["Title"] = "Generate Reports";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Generate Reports</h3>
                </div>
                <div class="card-body">
                    <form asp-action="Generate" method="post">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h5>Report Configuration</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label asp-for="ReportType" class="control-label">Report Type</label>
                                            <select asp-for="ReportType" class="form-control" required>
                                                <option value="">Select Report Type</option>
                                                <option value="booking">Booking Report</option>
                                                <option value="payment">Payment Report</option>
                                                <option value="guest">Guest Report</option>
                                            </select>
                                            <span asp-validation-for="ReportType" class="text-danger"></span>
                                        </div>

                                        <div class="form-group">
                                            <label asp-for="FromDate" class="control-label">From Date</label>
                                            <input asp-for="FromDate" class="form-control" type="date" />
                                            <span asp-validation-for="FromDate" class="text-danger"></span>
                                        </div>

                                        <div class="form-group">
                                            <label asp-for="ToDate" class="control-label">To Date</label>
                                            <input asp-for="ToDate" class="form-control" type="date" />
                                            <span asp-validation-for="ToDate" class="text-danger"></span>
                                        </div>

                                        <div class="form-group">
                                            <label asp-for="RoomTypeID" class="control-label">Room Type (Optional)</label>
                                            <select asp-for="RoomTypeID" class="form-control">
                                                <option value="">All Room Types</option>
                                                @if (Model.RoomTypes != null)
                                                {
                                                    @foreach (var roomType in Model.RoomTypes)
                                                    {
                                                        <option value="@roomType.RoomTypeID">@roomType.TypeName</option>
                                                    }
                                                }
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label asp-for="Status" class="control-label">Status (Optional)</label>
                                            <select asp-for="Status" class="form-control">
                                                <option value="">All Status</option>
                                                <option value="Confirmed">Confirmed</option>
                                                <option value="Pending">Pending</option>
                                                <option value="Cancelled">Cancelled</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h5>Report Types Description</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-calendar-check"></i> Booking Report</h6>
                                            <p class="mb-2">Comprehensive booking analysis including:</p>
                                            <ul class="mb-0">
                                                <li>Total bookings and revenue</li>
                                                <li>Occupancy rates</li>
                                                <li>Room type performance</li>
                                                <li>Monthly trends</li>
                                            </ul>
                                        </div>

                                        <div class="alert alert-success">
                                            <h6><i class="fas fa-credit-card"></i> Payment Report</h6>
                                            <p class="mb-2">Financial analysis including:</p>
                                            <ul class="mb-0">
                                                <li>Payment transactions</li>
                                                <li>Refund analysis</li>
                                                <li>Payment method breakdown</li>
                                                <li>Net revenue calculations</li>
                                            </ul>
                                        </div>

                                        <div class="alert alert-warning">
                                            <h6><i class="fas fa-users"></i> Guest Report</h6>
                                            <p class="mb-2">Guest analytics including:</p>
                                            <ul class="mb-0">
                                                <li>Guest demographics</li>
                                                <li>Returning vs new guests</li>
                                                <li>Geographic distribution</li>
                                                <li>Guest spending patterns</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-chart-bar"></i> Generate Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Set default dates (last month)
            var today = new Date();
            var lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
            
            if (!$('#FromDate').val()) {
                $('#FromDate').val(lastMonth.toISOString().split('T')[0]);
            }
            if (!$('#ToDate').val()) {
                $('#ToDate').val(today.toISOString().split('T')[0]);
            }
        });
    </script>
}
