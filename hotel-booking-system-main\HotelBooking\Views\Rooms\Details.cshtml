@model HotelBooking.Models.Room
@{
    ViewData["Title"] = "Room Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bed"></i> Room @Model.RoomNumber Details
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-@(Model.Status == "Available" ? "success" : Model.Status == "Occupied" ? "warning" : "danger") badge-lg">
                            @Model.Status
                        </span>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Room Information -->
                        <div class="col-md-6">
                            <div class="card card-outline card-info">
                                <div class="card-header">
                                    <h6 class="card-title"><i class="fas fa-info-circle"></i> Room Information</h6>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-4">Room Number:</dt>
                                        <dd class="col-sm-8"><strong>@Model.RoomNumber</strong></dd>

                                        <dt class="col-sm-4">Room Type:</dt>
                                        <dd class="col-sm-8">@Model.RoomType?.TypeName</dd>

                                        <dt class="col-sm-4">Price per Night:</dt>
                                        <dd class="col-sm-8">
                                            <span class="h5 text-primary">$@Model.Price</span>
                                        </dd>

                                        <dt class="col-sm-4">Max Occupancy:</dt>
                                        <dd class="col-sm-8">@Model.RoomType?.MaxOccupancy guests</dd>

                                        <dt class="col-sm-4">Bed Type:</dt>
                                        <dd class="col-sm-8">@(Model.BedType ?? "Not specified")</dd>

                                        <dt class="col-sm-4">View Type:</dt>
                                        <dd class="col-sm-8">@(Model.ViewType ?? "Not specified")</dd>

                                        <dt class="col-sm-4">Status:</dt>
                                        <dd class="col-sm-8">
                                            <span class="badge badge-@(Model.Status == "Available" ? "success" : Model.Status == "Occupied" ? "warning" : "danger")">
                                                @Model.Status
                                            </span>
                                        </dd>

                                        <dt class="col-sm-4">Active:</dt>
                                        <dd class="col-sm-8">
                                            @if (Model.IsActive)
                                            {
                                                <span class="badge badge-success">Active</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-danger">Inactive</span>
                                            }
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <!-- Room Details -->
                        <div class="col-md-6">
                            <div class="card card-outline card-secondary">
                                <div class="card-header">
                                    <h6 class="card-title"><i class="fas fa-clipboard-list"></i> Additional Details</h6>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-4">Description:</dt>
                                        <dd class="col-sm-8">
                                            @if (!string.IsNullOrEmpty(Model.Description))
                                            {
                                                <p>@Model.Description</p>
                                            }
                                            else
                                            {
                                                <span class="text-muted">No description available</span>
                                            }
                                        </dd>

                                        <dt class="col-sm-4">Created Date:</dt>
                                        <dd class="col-sm-8">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</dd>

                                        <dt class="col-sm-4">Created By:</dt>
                                        <dd class="col-sm-8">@(Model.CreatedBy ?? "System")</dd>

                                        @if (Model.ModifiedDate.HasValue)
                                        {
                                            <dt class="col-sm-4">Modified Date:</dt>
                                            <dd class="col-sm-8">@Model.ModifiedDate.Value.ToString("dd/MM/yyyy HH:mm")</dd>

                                            <dt class="col-sm-4">Modified By:</dt>
                                            <dd class="col-sm-8">@(Model.ModifiedBy ?? "System")</dd>
                                        }
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Room Amenities -->
                    @if (Model.RoomAmenities != null && Model.RoomAmenities.Any())
                    {
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card card-outline card-success">
                                    <div class="card-header">
                                        <h6 class="card-title"><i class="fas fa-star"></i> Room Amenities</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            @foreach (var roomAmenity in Model.RoomAmenities.OrderBy(ra => ra.Amenity?.Category).ThenBy(ra => ra.Amenity?.AmenityName))
                                            {
                                                <div class="col-md-3 col-sm-4 col-6 mb-3">
                                                    <div class="amenity-item">
                                                        @if (!string.IsNullOrEmpty(roomAmenity.Amenity?.Icon))
                                                        {
                                                            <i class="@roomAmenity.Amenity.Icon text-primary"></i>
                                                        }
                                                        else
                                                        {
                                                            <i class="fas fa-check text-success"></i>
                                                        }
                                                        <span class="ml-2">@roomAmenity.Amenity?.AmenityName</span>
                                                        @if (!string.IsNullOrEmpty(roomAmenity.Amenity?.Category))
                                                        {
                                                            <small class="text-muted d-block ml-4">@roomAmenity.Amenity.Category</small>
                                                        }
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    No amenities assigned to this room yet.
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Room Type Details -->
                    @if (Model.RoomType != null)
                    {
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card card-outline card-warning">
                                    <div class="card-header">
                                        <h6 class="card-title"><i class="fas fa-home"></i> Room Type Information</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <dl class="row">
                                                    <dt class="col-sm-4">Type Name:</dt>
                                                    <dd class="col-sm-8">@Model.RoomType.TypeName</dd>

                                                    <dt class="col-sm-4">Max Occupancy:</dt>
                                                    <dd class="col-sm-8">@Model.RoomType.MaxOccupancy guests</dd>
                                                </dl>
                                            </div>
                                            <div class="col-md-6">
                                                <dl class="row">
                                                    <dt class="col-sm-4">Description:</dt>
                                                    <dd class="col-sm-8">
                                                        @if (!string.IsNullOrEmpty(Model.RoomType.Description))
                                                        {
                                                            @Model.RoomType.Description
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">No description</span>
                                                        }
                                                    </dd>
                                                </dl>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <div class="card-footer">
                    @if (User.IsInRole("Admin") || User.IsInRole("Staff"))
                    {
                        <a href="@Url.Action("Edit", new { id = Model.RoomID })" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Room
                        </a>
                        
                        <button type="button" class="btn btn-@(Model.IsActive ? "secondary" : "success")" 
                                onclick="toggleRoomStatus(@Model.RoomID)">
                            @if (Model.IsActive)
                            {
                                <i class="fas fa-ban"></i> @("Deactivate")
                            }
                            else
                            {
                                <i class="fas fa-check"></i> @("Activate")
                            }
                        </button>

                        @if (User.IsInRole("Admin"))
                        {
                            <a href="@Url.Action("Delete", new { id = Model.RoomID })" 
                               class="btn btn-danger"
                               onclick="return confirm('Are you sure you want to delete this room?')">
                                <i class="fas fa-trash"></i> Delete Room
                            </a>
                        }
                    }

                    @if (Model.Status == "Available")
                    {
                        <a href="@Url.Action("Book", "Reservations", new { roomId = Model.RoomID })" class="btn btn-primary">
                            <i class="fas fa-calendar-plus"></i> Book This Room
                        </a>
                    }

                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Rooms
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.amenity-item {
    padding: 8px;
    border-radius: 4px;
    background-color: #f8f9fa;
    margin-bottom: 5px;
}

.amenity-item:hover {
    background-color: #e9ecef;
}
</style>

@section Scripts {
    <script>
        function toggleRoomStatus(roomId) {
            $.post('@Url.Action("ToggleStatus")', { id: roomId }, function(result) {
                if (result.success) {
                    location.reload();
                } else {
                    alert('Error: ' + result.message);
                }
            });
        }
    </script>
}
