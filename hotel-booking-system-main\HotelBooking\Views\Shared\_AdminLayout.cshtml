<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - HotelBooking Admin</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/HotelBooking.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
</head>
<body class="admin-body">
    <!-- Admin Header -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom shadow-sm">
        <div class="container-fluid px-4">
            <!-- Brand -->
            <a class="navbar-brand d-flex align-items-center" href="#">
                <div class="brand-icon me-3">
                    <i class="fas fa-hotel text-primary fa-2x"></i>
                </div>
                <span class="fw-bold fs-3 text-primary">HotelBooking</span>
            </a>

            <!-- Main Navigation -->
            <div class="navbar-nav d-flex flex-row mx-auto">
                <a class="nav-link px-4 py-2 mx-1 admin-nav-link" href="@Url.Action("Dashboard", "Admin")">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle px-4 py-2 mx-1 admin-nav-link" href="#" id="managementDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cogs me-2"></i>Management
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="@Url.Action("Index", "GuestProfile")"><i class="fas fa-users me-2"></i>Guest Profiles</a></li>
                        <li><a class="dropdown-item" href="@Url.Action("Index", "PaymentProcess")"><i class="fas fa-credit-card me-2"></i>Process Payments</a></li>
                        <li><a class="dropdown-item" href="@Url.Action("Index", "FeedbackManagement")"><i class="fas fa-comments me-2"></i>Manage Feedback</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="@Url.Action("Index", "Report")"><i class="fas fa-chart-bar me-2"></i>Generate Reports</a></li>
                        <li><a class="dropdown-item" href="@Url.Action("Index", "Notification")"><i class="fas fa-bell me-2"></i>Send Notifications</a></li>
                    </ul>
                </div>
                <a class="nav-link px-4 py-2 mx-1 admin-nav-link" href="#">
                    <i class="fas fa-bed me-2"></i>Rooms
                </a>
                <a class="nav-link px-4 py-2 mx-1 admin-nav-link active" href="@Url.Action("Reservations", "Admin")">
                    <i class="fas fa-calendar-check me-2"></i>Reservations
                </a>
            </div>

            <!-- User Menu -->
            <div class="navbar-nav">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <div class="notification-badge me-3">
                            <i class="fas fa-bell text-muted"></i>
                            <span class="badge bg-danger position-absolute top-0 start-100 translate-middle rounded-pill">1</span>
                        </div>
                        <div class="user-info d-flex align-items-center">
                            <span class="user-initial bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                @(User.Identity?.Name?.Substring(0, 1).ToUpper() ?? "A")
                            </span>
                            <div class="d-flex flex-column">
                                <span class="fw-semibold">@User.Identity?.Name</span>
                                <small class="text-success">Admin</small>
                            </div>
                        </div>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow border-0">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @RenderBody()
    </main>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
