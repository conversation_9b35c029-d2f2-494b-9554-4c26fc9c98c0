@model HotelBooking.Models.ViewModels.UserDetailsViewModel
@{
    ViewData["Title"] = "User Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user"></i> User Details
                    </h3>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Username:</dt>
                                <dd class="col-sm-8">@Model.UserName</dd>

                                <dt class="col-sm-4">Email:</dt>
                                <dd class="col-sm-8">@Model.Email</dd>

                                <dt class="col-sm-4">Phone:</dt>
                                <dd class="col-sm-8">@(Model.PhoneNumber ?? "Not provided")</dd>

                                <dt class="col-sm-4">Role:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge badge-info">@Model.RoleName</span>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge badge-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-danger">Inactive</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">Created Date:</dt>
                                <dd class="col-sm-8">@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</dd>

                                <dt class="col-sm-4">Last Login:</dt>
                                <dd class="col-sm-8">
                                    @if (Model.LastLogin.HasValue)
                                    {
                                        @Model.LastLogin.Value.ToString("dd/MM/yyyy HH:mm")
                                    }
                                    else
                                    {
                                        <span class="text-muted">Never logged in</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">Created By:</dt>
                                <dd class="col-sm-8">@(Model.CreatedBy ?? "System")</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <a href="@Url.Action("Edit", new { id = Model.UserId })" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit User
                    </a>
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                    <button type="button" class="btn btn-info" onclick="resetPassword(@Model.UserId)">
                        <i class="fas fa-key"></i> Reset Password
                    </button>
                    <button type="button" class="btn btn-@(Model.IsActive ? "danger" : "success")" 
                            onclick="toggleUserStatus(@Model.UserId)">
                        @if (Model.IsActive)
                        {
                            <i class="fas fa-ban"></i> @("Deactivate")
                        }
                        else
                        {
                            <i class="fas fa-check"></i> @("Activate")
                        }
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reset Password</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="resetPasswordForm">
                    <div class="form-group">
                        <label for="newPassword">New Password:</label>
                        <input type="password" class="form-control" id="newPassword" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">Confirm Password:</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmResetPassword()">Reset Password</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let currentUserId = 0;

        function resetPassword(userId) {
            currentUserId = userId;
            $('#resetPasswordModal').modal('show');
        }

        function confirmResetPassword() {
            const newPassword = $('#newPassword').val();
            const confirmPassword = $('#confirmPassword').val();

            if (newPassword !== confirmPassword) {
                alert('Passwords do not match!');
                return;
            }

            if (newPassword.length < 6) {
                alert('Password must be at least 6 characters long!');
                return;
            }

            $.post('@Url.Action("ResetPassword")', { 
                id: currentUserId, 
                newPassword: newPassword 
            }, function(result) {
                if (result.success) {
                    alert('Password reset successfully!');
                    $('#resetPasswordModal').modal('hide');
                } else {
                    alert('Error: ' + result.message);
                }
            });
        }

        function toggleUserStatus(userId) {
            if (confirm('Are you sure you want to change this user\'s status?')) {
                $.post('@Url.Action("ToggleStatus")', { id: userId }, function(result) {
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + result.message);
                    }
                });
            }
        }
    </script>
}
