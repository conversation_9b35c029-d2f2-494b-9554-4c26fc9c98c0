using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HotelBooking.Data;
using HotelBooking.Models;
using HotelBooking.Models.ViewModels;
using Microsoft.AspNetCore.Authorization;

namespace HotelBooking.Controllers
{
    public class RoomsController : Controller
    {
        private readonly HotelBookingContext _context;

        public RoomsController(HotelBookingContext context)
        {
            _context = context;
        }

        // GET: Rooms
        public async Task<IActionResult> Index()
        {
            var rooms = await _context.Rooms
                .Include(r => r.RoomType)
                .Where(r => r.IsActive)
                .ToListAsync();
            return View(rooms);
        }

        // GET: Rooms/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var room = await _context.Rooms
                .Include(r => r.RoomType)
                .Include(r => r.RoomAmenities!)
                    .ThenInclude(ra => ra.Amenity)
                .FirstOrDefaultAsync(m => m.RoomID == id);

            if (room == null)
            {
                return NotFound();
            }

            return View(room);
        }

        // GET: Rooms/Available
        public async Task<IActionResult> Available(DateTime? checkIn, DateTime? checkOut, int? guests)
        {
            var query = _context.Rooms
                .Include(r => r.RoomType)
                .Where(r => r.IsActive && r.Status == "Available");

            if (checkIn.HasValue && checkOut.HasValue)
            {
                // Check for rooms that are not booked during the specified period
                var bookedRoomIds = await _context.Reservations
                    .Where(res => res.CheckInDate < checkOut && res.CheckOutDate > checkIn)
                    .Select(res => res.RoomID)
                    .ToListAsync();

                query = query.Where(r => !bookedRoomIds.Contains(r.RoomID));
            }

            if (guests.HasValue)
            {
                query = query.Where(r => r.RoomType!.MaxOccupancy >= guests);
            }

            var availableRooms = await query.ToListAsync();

            ViewBag.CheckIn = checkIn?.ToString("yyyy-MM-dd");
            ViewBag.CheckOut = checkOut?.ToString("yyyy-MM-dd");
            ViewBag.Guests = guests;

            return View(availableRooms);
        }

        // GET: Rooms/Search
        public IActionResult Search()
        {
            ViewBag.RoomTypes = _context.RoomTypes.Where(rt => rt.IsActive).ToList();
            return View();
        }

        // POST: Rooms/Search
        [HttpPost]
        public async Task<IActionResult> Search(DateTime checkIn, DateTime checkOut, int guests, int? roomTypeId, decimal? maxPrice)
        {
            var query = _context.Rooms
                .Include(r => r.RoomType)
                .Where(r => r.IsActive && r.Status == "Available");

            // Check availability
            var bookedRoomIds = await _context.Reservations
                .Where(res => res.CheckInDate < checkOut && res.CheckOutDate > checkIn)
                .Select(res => res.RoomID)
                .ToListAsync();

            query = query.Where(r => !bookedRoomIds.Contains(r.RoomID));

            // Filter by guest capacity
            query = query.Where(r => r.RoomType!.MaxOccupancy >= guests);

            // Filter by room type
            if (roomTypeId.HasValue)
            {
                query = query.Where(r => r.RoomTypeID == roomTypeId);
            }

            // Filter by max price
            if (maxPrice.HasValue)
            {
                query = query.Where(r => r.Price <= maxPrice);
            }

            var searchResults = await query.ToListAsync();

            ViewBag.CheckIn = checkIn.ToString("yyyy-MM-dd");
            ViewBag.CheckOut = checkOut.ToString("yyyy-MM-dd");
            ViewBag.Guests = guests;
            ViewBag.RoomTypeId = roomTypeId;
            ViewBag.MaxPrice = maxPrice;
            ViewBag.RoomTypes = _context.RoomTypes.Where(rt => rt.IsActive).ToList();

            return View("SearchResults", searchResults);
        }

        // GET: Rooms/Book/5
        [Authorize]
        public async Task<IActionResult> Book(int? id, DateTime? checkIn, DateTime? checkOut, int? guests)
        {
            if (id == null)
            {
                return NotFound();
            }

            var room = await _context.Rooms
                .Include(r => r.RoomType)
                .FirstOrDefaultAsync(r => r.RoomID == id);

            if (room == null)
            {
                return NotFound();
            }

            var bookingViewModel = new BookingViewModel
            {
                RoomID = room.RoomID,
                Room = room,
                CheckInDate = checkIn ?? DateTime.Today.AddDays(1),
                CheckOutDate = checkOut ?? DateTime.Today.AddDays(2),
                NumberOfGuests = guests ?? 1
            };

            return View(bookingViewModel);
        }

        // POST: Rooms/Book
        [HttpPost]
        [Authorize]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Book(BookingViewModel model)
        {
            if (ModelState.IsValid)
            {
                // Check if room is still available
                var isAvailable = !await _context.Reservations
                    .AnyAsync(res => res.RoomID == model.RoomID &&
                                   res.CheckInDate < model.CheckOutDate &&
                                   res.CheckOutDate > model.CheckInDate);

                if (!isAvailable)
                {
                    ModelState.AddModelError("", "Sorry, this room is no longer available for the selected dates.");
                    model.Room = await _context.Rooms
                        .Include(r => r.RoomType)
                        .FirstOrDefaultAsync(r => r.RoomID == model.RoomID);
                    return View(model);
                }

                var reservation = new Reservation
                {
                    UserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0"),
                    RoomID = model.RoomID,
                    BookingDate = DateTime.Now,
                    CheckInDate = model.CheckInDate,
                    CheckOutDate = model.CheckOutDate,
                    NumberOfGuests = model.NumberOfGuests,
                    Status = "Confirmed",
                    CreatedBy = User.Identity?.Name ?? "System",
                    CreatedDate = DateTime.Now
                };

                _context.Reservations.Add(reservation);
                await _context.SaveChangesAsync();

                TempData["Message"] = "Room booked successfully!";
                return RedirectToAction("Details", "Reservations", new { id = reservation.ReservationID });
            }

            model.Room = await _context.Rooms
                .Include(r => r.RoomType)
                .FirstOrDefaultAsync(r => r.RoomID == model.RoomID);
            return View(model);
        }

        // GET: Rooms/Create
        [Authorize(Roles = "Admin,Staff")]
        public async Task<IActionResult> Create()
        {
            var viewModel = new RoomViewModel
            {
                IsActive = true,
                Status = "Available",
                RoomTypes = await _context.RoomTypes.Where(rt => rt.IsActive).ToListAsync(),
                Amenities = await _context.Amenities.Where(a => a.IsActive).ToListAsync()
            };

            return View(viewModel);
        }

        // POST: Rooms/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin,Staff")]
        public async Task<IActionResult> Create(RoomViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                var room = new Room
                {
                    RoomNumber = viewModel.RoomNumber,
                    RoomTypeID = viewModel.RoomTypeID,
                    Price = viewModel.Price,
                    Status = viewModel.Status,
                    Description = viewModel.Description,
                    IsActive = viewModel.IsActive,
                    CreatedBy = User.Identity?.Name ?? "System",
                    CreatedDate = DateTime.Now
                };

                _context.Rooms.Add(room);
                await _context.SaveChangesAsync();

                // Add amenities
                if (viewModel.SelectedAmenityIds != null)
                {
                    foreach (var amenityId in viewModel.SelectedAmenityIds)
                    {
                        _context.RoomAmenities.Add(new RoomAmenity
                        {
                            RoomID = room.RoomID,
                            AmenityID = amenityId
                        });
                    }
                    await _context.SaveChangesAsync();
                }

                TempData["SuccessMessage"] = "Room created successfully.";
                return RedirectToAction(nameof(Index));
            }

            viewModel.RoomTypes = await _context.RoomTypes.Where(rt => rt.IsActive).ToListAsync();
            viewModel.Amenities = await _context.Amenities.Where(a => a.IsActive).ToListAsync();
            return View(viewModel);
        }

        // GET: Rooms/Edit/5
        [Authorize(Roles = "Admin,Staff")]
        public async Task<IActionResult> Edit(int id)
        {
            var room = await _context.Rooms
                .Include(r => r.RoomAmenities!)
                .FirstOrDefaultAsync(r => r.RoomID == id);

            if (room == null)
            {
                return NotFound();
            }

            var viewModel = new RoomViewModel
            {
                RoomID = room.RoomID,
                RoomNumber = room.RoomNumber,
                RoomTypeID = room.RoomTypeID,
                Price = room.Price,
                Status = room.Status,
                Description = room.Description,
                IsActive = room.IsActive,
                RoomTypes = await _context.RoomTypes.Where(rt => rt.IsActive).ToListAsync(),
                Amenities = await _context.Amenities.Where(a => a.IsActive).ToListAsync(),
                SelectedAmenityIds = room.RoomAmenities?.Select(ra => ra.AmenityID).ToList() ?? new List<int>()
            };

            return View(viewModel);
        }

        // POST: Rooms/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin,Staff")]
        public async Task<IActionResult> Edit(int id, RoomViewModel viewModel)
        {
            if (id != viewModel.RoomID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var room = await _context.Rooms
                        .Include(r => r.RoomAmenities!)
                        .FirstOrDefaultAsync(r => r.RoomID == id);

                    if (room == null)
                    {
                        return NotFound();
                    }

                    room.RoomNumber = viewModel.RoomNumber;
                    room.RoomTypeID = viewModel.RoomTypeID;
                    room.Price = viewModel.Price;
                    room.Status = viewModel.Status;
                    room.Description = viewModel.Description;
                    room.IsActive = viewModel.IsActive;
                    room.ModifiedBy = User.Identity?.Name ?? "System";
                    room.ModifiedDate = DateTime.Now;

                    // Update amenities
                    _context.RoomAmenities.RemoveRange(room.RoomAmenities!);
                    if (viewModel.SelectedAmenityIds != null)
                    {
                        foreach (var amenityId in viewModel.SelectedAmenityIds)
                        {
                            _context.RoomAmenities.Add(new RoomAmenity
                            {
                                RoomID = room.RoomID,
                                AmenityID = amenityId
                            });
                        }
                    }

                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Room updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!RoomExists(viewModel.RoomID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            viewModel.RoomTypes = await _context.RoomTypes.Where(rt => rt.IsActive).ToListAsync();
            viewModel.Amenities = await _context.Amenities.Where(a => a.IsActive).ToListAsync();
            return View(viewModel);
        }

        // GET: Rooms/Delete/5
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(int id)
        {
            var room = await _context.Rooms
                .Include(r => r.RoomType)
                .Include(r => r.RoomAmenities!)
                    .ThenInclude(ra => ra.Amenity)
                .FirstOrDefaultAsync(r => r.RoomID == id);

            if (room == null)
            {
                return NotFound();
            }

            return View(room);
        }

        // POST: Rooms/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var room = await _context.Rooms.FindAsync(id);
            if (room != null)
            {
                // Check if room has reservations
                var hasReservations = await _context.Reservations.AnyAsync(r => r.RoomID == id);
                if (hasReservations)
                {
                    TempData["ErrorMessage"] = "Cannot delete room because it has reservations.";
                    return RedirectToAction(nameof(Index));
                }

                // Remove room amenities first
                var roomAmenities = await _context.RoomAmenities.Where(ra => ra.RoomID == id).ToListAsync();
                _context.RoomAmenities.RemoveRange(roomAmenities);

                _context.Rooms.Remove(room);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Room deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Rooms/ToggleStatus/5
        [HttpPost]
        [Authorize(Roles = "Admin,Staff")]
        public async Task<IActionResult> ToggleStatus(int id)
        {
            var room = await _context.Rooms.FindAsync(id);
            if (room == null)
            {
                return Json(new { success = false, message = "Room not found" });
            }

            room.IsActive = !room.IsActive;
            room.ModifiedBy = User.Identity?.Name ?? "System";
            room.ModifiedDate = DateTime.Now;

            await _context.SaveChangesAsync();

            return Json(new { success = true, isActive = room.IsActive });
        }

        private bool RoomExists(int id)
        {
            return _context.Rooms.Any(e => e.RoomID == id);
        }
    }
}
