html {
  font-size: 16px;
}

@media (min-width: 768px) {
  html {
    font-size: 18px;
  }
}

@media (min-width: 1200px) {
  html {
    font-size: 20px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  line-height: 1.6;
}

/* Enhanced Typography */
h1, .h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

h2, .h2 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1.25rem;
}

h3, .h3 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

h4, .h4 {
  font-size: 1.75rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

h5, .h5 {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

p, .lead {
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.lead {
  font-size: 1.3rem;
  font-weight: 300;
}

/* Enhanced Buttons */
.btn {
  font-size: 1.1rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-lg {
  font-size: 1.3rem;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
}

.btn-sm {
  font-size: 1rem;
  padding: 0.5rem 1rem;
}

/* Enhanced Form Controls */
.form-control, .form-select {
  font-size: 1.1rem;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  border: 2px solid #dee2e6;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* Enhanced Cards */
.card {
  border-radius: 1rem;
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
}

.card-body {
  padding: 2rem;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.card-text {
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Enhanced Navigation */
.navbar {
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-size: 1.8rem;
  font-weight: 700;
}

.nav-link {
  font-size: 1.1rem;
  font-weight: 500;
  padding: 0.75rem 1rem !important;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background-color: rgba(13, 110, 253, 0.1);
}

/* Enhanced Alerts */
.alert {
  font-size: 1.1rem;
  padding: 1rem 1.5rem;
  border-radius: 0.75rem;
  border: none;
  margin-bottom: 1.5rem;
}

/* Enhanced Tables */
.table {
  font-size: 1.1rem;
}

.table th {
  font-weight: 600;
  padding: 1rem;
  border-bottom: 2px solid #dee2e6;
}

.table td {
  padding: 1rem;
}

/* Enhanced Badges */
.badge {
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
}

/* Enhanced Spacing */
.container {
  padding-left: 2rem;
  padding-right: 2rem;
}

@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  h1, .h1 {
    font-size: 2.5rem;
  }

  h2, .h2 {
    font-size: 2rem;
  }

  .btn {
    font-size: 1rem;
    padding: 0.75rem 1.25rem;
  }
}

/* Admin Layout Styles */
.admin-body {
    background-color: #f8f9fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.brand-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.admin-nav-link {
    color: #6c757d !important;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.admin-nav-link:hover {
    background-color: #e9ecef;
    color: #495057 !important;
}

.admin-nav-link.active {
    background-color: #e7f3ff;
    color: #0066cc !important;
}

.notification-badge {
    position: relative;
    width: 24px;
    height: 24px;
}

.user-initial {
    width: 32px;
    height: 32px;
    font-size: 14px;
    font-weight: 600;
}

.user-info {
    font-size: 14px;
}