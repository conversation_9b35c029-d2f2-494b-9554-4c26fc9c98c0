@model HotelBooking.Models.Feedback
@{
    ViewData["Title"] = "Feedback Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-comment-alt"></i> Feedback Details
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-@(Model.Rating >= 4 ? "success" : Model.Rating >= 3 ? "warning" : "danger") badge-lg">
                            @for (int i = 1; i <= 5; i++)
                            {
                                if (i <= Model.Rating)
                                {
                                    <i class="fas fa-star"></i>
                                }
                                else
                                {
                                    <i class="far fa-star"></i>
                                }
                            }
                            (@Model.Rating/5)
                        </span>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Guest Information -->
                        <div class="col-md-6">
                            <div class="card card-outline card-info">
                                <div class="card-header">
                                    <h6 class="card-title"><i class="fas fa-user"></i> Guest Information</h6>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-4">Name:</dt>
                                        <dd class="col-sm-8">@Model.Guest?.FirstName @Model.Guest?.LastName</dd>

                                        <dt class="col-sm-4">Email:</dt>
                                        <dd class="col-sm-8">@Model.Guest?.Email</dd>

                                        <dt class="col-sm-4">Phone:</dt>
                                        <dd class="col-sm-8">@(Model.Guest?.PhoneNumber ?? "Not provided")</dd>

                                        <dt class="col-sm-4">Feedback Date:</dt>
                                        <dd class="col-sm-8">@Model.FeedbackDate.ToString("dd/MM/yyyy HH:mm")</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <!-- Reservation Information -->
                        <div class="col-md-6">
                            <div class="card card-outline card-primary">
                                <div class="card-header">
                                    <h6 class="card-title"><i class="fas fa-calendar-check"></i> Reservation Details</h6>
                                </div>
                                <div class="card-body">
                                    @if (Model.Reservation != null)
                                    {
                                        <dl class="row">
                                            <dt class="col-sm-4">Reservation ID:</dt>
                                            <dd class="col-sm-8">#@Model.ReservationID</dd>

                                            <dt class="col-sm-4">Room:</dt>
                                            <dd class="col-sm-8">
                                                @Model.Reservation.Room?.RoomNumber 
                                                (@Model.Reservation.Room?.RoomType?.TypeName)
                                            </dd>

                                            <dt class="col-sm-4">Check-in:</dt>
                                            <dd class="col-sm-8">@Model.Reservation.CheckInDate.ToString("dd/MM/yyyy")</dd>

                                            <dt class="col-sm-4">Check-out:</dt>
                                            <dd class="col-sm-8">@Model.Reservation.CheckOutDate.ToString("dd/MM/yyyy")</dd>

                                            <dt class="col-sm-4">Stay Duration:</dt>
                                            <dd class="col-sm-8">@((Model.Reservation.CheckOutDate - Model.Reservation.CheckInDate).Days) nights</dd>
                                        </dl>
                                    }
                                    else
                                    {
                                        <p class="text-muted">No reservation information available</p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Feedback Content -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card card-outline card-warning">
                                <div class="card-header">
                                    <h6 class="card-title"><i class="fas fa-comment"></i> Feedback Content</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <strong>Overall Rating:</strong>
                                            <div class="rating-display">
                                                @for (int i = 1; i <= 5; i++)
                                                {
                                                    if (i <= Model.Rating)
                                                    {
                                                        <i class="fas fa-star text-warning"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="far fa-star text-muted"></i>
                                                    }
                                                }
                                                <span class="ml-2">(@Model.Rating out of 5)</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Feedback Category:</strong>
                                            <span class="badge badge-info ml-2">
                                                @(string.IsNullOrEmpty(Model.Category) ? "General" : Model.Category)
                                            </span>
                                        </div>
                                    </div>

                                    @if (!string.IsNullOrEmpty(Model.Comments))
                                    {
                                        <div class="feedback-comments">
                                            <strong>Comments:</strong>
                                            <div class="mt-2 p-3 bg-light rounded">
                                                <p class="mb-0">@Model.Comments</p>
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i>
                                            No written comments provided with this feedback.
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Response Section -->
                    @if (!string.IsNullOrEmpty(Model.Response))
                    {
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card card-outline card-success">
                                    <div class="card-header">
                                        <h6 class="card-title"><i class="fas fa-reply"></i> Management Response</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="response-content p-3 bg-light rounded">
                                            <p class="mb-2">@Model.Response</p>
                                            @if (Model.ResponseDate.HasValue)
                                            {
                                                <small class="text-muted">
                                                    <i class="fas fa-clock"></i> 
                                                    Responded on @Model.ResponseDate.Value.ToString("dd/MM/yyyy HH:mm")
                                                    @if (!string.IsNullOrEmpty(Model.ResponseBy))
                                                    {
                                                        <span> by @Model.ResponseBy</span>
                                                    }
                                                </small>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>No Response Yet</strong> - This feedback has not been responded to.
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <div class="card-footer">
                    @if (User.IsInRole("Admin") || User.IsInRole("Staff"))
                    {
                        @if (string.IsNullOrEmpty(Model.Response))
                        {
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#responseModal">
                                <i class="fas fa-reply"></i> Add Response
                            </button>
                        }
                        else
                        {
                            <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#responseModal">
                                <i class="fas fa-edit"></i> Edit Response
                            </button>
                        }
                        
                        <button type="button" class="btn btn-info" onclick="markAsResolved(@Model.FeedbackID)">
                            <i class="fas fa-check"></i> Mark as Resolved
                        </button>
                    }
                    
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Feedback List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Response Modal -->
<div class="modal fade" id="responseModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    @(string.IsNullOrEmpty(Model.Response) ? "Add Response" : "Edit Response")
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="responseForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="responseText">Response:</label>
                        <textarea class="form-control" id="responseText" rows="5" 
                                  placeholder="Enter your response to this feedback...">@Model.Response</textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        This response will be visible to the guest and may be sent via email.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveResponse()">
                        <i class="fas fa-save"></i> Save Response
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function saveResponse() {
            const response = $('#responseText').val();
            if (!response.trim()) {
                alert('Please enter a response.');
                return;
            }

            $.post('@Url.Action("AddResponse", "FeedbackManagement")', {
                id: @Model.FeedbackID,
                response: response
            }, function(result) {
                if (result.success) {
                    location.reload();
                } else {
                    alert('Error: ' + result.message);
                }
            });
        }

        function markAsResolved(feedbackId) {
            if (confirm('Mark this feedback as resolved?')) {
                $.post('@Url.Action("MarkAsResolved", "FeedbackManagement")', {
                    id: feedbackId
                }, function(result) {
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + result.message);
                    }
                });
            }
        }
    </script>
}
