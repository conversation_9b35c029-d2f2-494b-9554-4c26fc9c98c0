@model List<object>
@{
    ViewData["Title"] = "Database Check - Users";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-4">
                <h2 class="text-primary">🔍 Database Check - Users</h2>
                <p class="text-muted">Kiểm tra dữ liệu users trong database</p>
            </div>

            @if (ViewBag.Message != null)
            {
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> @ViewBag.Message
                </div>
            }

            @if (ViewBag.Error != null)
            {
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> @ViewBag.Error
                </div>
            }

            @if (Model != null && Model.Count > 0)
            {
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Email Confirmed</th>
                                <th>Active</th>
                                <th>Role ID</th>
                                <th>Roles</th>
                                <th>Created Date</th>
                                <th>Created By</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (dynamic user in Model)
                            {
                                <tr>
                                    <td><code>@user.Id</code></td>
                                    <td><strong>@user.UserName</strong></td>
                                    <td>@user.Email</td>
                                    <td>
                                        @if (user.EmailConfirmed)
                                        {
                                            <span class="badge bg-success">✓ Confirmed</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">⚠ Not Confirmed</span>
                                        }
                                    </td>
                                    <td>
                                        @if (user.IsActive)
                                        {
                                            <span class="badge bg-success">✓ Active</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">✗ Inactive</span>
                                        }
                                    </td>
                                    <td>@user.RoleID</td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(user.Roles))
                                        {
                                            <span class="badge bg-primary">@user.Roles</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">No Role</span>
                                        }
                                    </td>
                                    <td>@user.CreatedDate?.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>@user.CreatedBy</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="alert alert-warning text-center">
                    <h4><i class="fas fa-exclamation-triangle"></i> Không có dữ liệu</h4>
                    <p>Không tìm thấy user nào trong database. Có thể seed data chưa chạy hoặc có lỗi.</p>
                </div>
            }

            <div class="text-center mt-4">
                <a href="/Account/DemoAccounts" class="btn btn-primary">
                    <i class="fas fa-users"></i> Xem Demo Accounts
                </a>
                <a href="/Account/Login" class="btn btn-outline-secondary">
                    <i class="fas fa-sign-in-alt"></i> Đăng nhập
                </a>
                <a href="/" class="btn btn-outline-primary">
                    <i class="fas fa-home"></i> Trang chủ
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    white-space: nowrap;
}
.table td {
    vertical-align: middle;
}
code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.85em;
}
</style>
