@model HotelBooking.Models.ViewModels.GuestProfileViewModel
@{
    ViewData["Title"] = "Delete Guest";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Delete Guest</h3>
                    <div class="card-tools">
                        <a href="@Url.Action("Index")" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h4><i class="fas fa-exclamation-triangle"></i> Warning!</h4>
                        Are you sure you want to delete this guest profile? This action cannot be undone.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>Personal Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Guest ID:</strong></td>
                                    <td>@Model.GuestID</td>
                                </tr>
                                <tr>
                                    <td><strong>First Name:</strong></td>
                                    <td>@Model.FirstName</td>
                                </tr>
                                <tr>
                                    <td><strong>Last Name:</strong></td>
                                    <td>@Model.LastName</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>@Model.Email</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>@Model.Phone</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Address Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Country:</strong></td>
                                    <td>@Model.CountryName</td>
                                </tr>
                                <tr>
                                    <td><strong>State:</strong></td>
                                    <td>@Model.StateName</td>
                                </tr>
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td>@Model.UserName</td>
                                </tr>
                                <tr>
                                    <td><strong>Created Date:</strong></td>
                                    <td>@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="mt-3">
                        <form asp-action="Delete" method="post" style="display: inline;">
                            <input type="hidden" asp-for="GuestID" />
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this guest?')">
                                <i class="fas fa-trash"></i> Delete Guest
                            </button>
                        </form>
                        <a href="@Url.Action("Details", new { id = Model.GuestID })" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
