@model HotelBooking.Models.Guest
@{
    ViewData["Title"] = "My Profile";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-circle me-2"></i>My Profile
                    </h4>
                </div>
                <div class="card-body">
                    @if (ViewBag.Message != null)
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>@ViewBag.Message
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">First Name:</label>
                                <p class="form-control-plaintext">@Model.FirstName</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Last Name:</label>
                                <p class="form-control-plaintext">@Model.LastName</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email:</label>
                                <p class="form-control-plaintext">@Model.Email</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Phone:</label>
                                <p class="form-control-plaintext">@Model.Phone</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Address:</label>
                        <p class="form-control-plaintext">@Model.Address</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Country:</label>
                                <p class="form-control-plaintext">@Model.Country?.CountryName</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">State:</label>
                                <p class="form-control-plaintext">@Model.State?.StateName</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Age Group:</label>
                        <p class="form-control-plaintext">@Model.AgeGroup</p>
                    </div>

                    <div class="text-center mt-4">
                        <button class="btn btn-warning" onclick="alert('Edit profile functionality coming soon!')">
                            <i class="fas fa-edit me-2"></i>Edit Profile
                        </button>
                        <a href="@Url.Action("Index", "Reservations")" class="btn btn-primary ms-2">
                            <i class="fas fa-bookmark me-2"></i>My Bookings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
