@{
    ViewData["Title"] = "Access Denied";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-lock fa-5x text-danger"></i>
                    </div>
                    <h2 class="card-title text-danger">Access Denied</h2>
                    <p class="card-text">
                        You don't have permission to access this resource.
                    </p>
                    <p class="text-muted">
                        If you believe this is an error, please contact your administrator.
                    </p>
                    <div class="mt-4">
                        <a href="@Url.Action("Index", "Home")" class="btn btn-primary me-2">
                            <i class="fas fa-home me-2"></i>Go Home
                        </a>
                        <a href="javascript:history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Go Back
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
