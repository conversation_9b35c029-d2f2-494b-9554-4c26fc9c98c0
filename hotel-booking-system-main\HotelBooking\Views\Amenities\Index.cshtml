@model HotelBooking.Models.ViewModels.AmenityListViewModel
@{
    ViewData["Title"] = "Amenities Management";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-star"></i> Amenities Management
                    </h3>
                    <div class="card-tools">
                        <a href="@Url.Action("Create")" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New Amenity
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" name="searchTerm" value="@Model.SearchTerm" 
                                       class="form-control" placeholder="Search amenities...">
                            </div>
                            <div class="col-md-2">
                                <select name="categoryFilter" class="form-control">
                                    <option value="">All Categories</option>
                                    @foreach (var category in Model.Categories)
                                    {
                                        <option value="@category" selected="@(Model.CategoryFilter == category)">
                                            @category
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="statusFilter" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="Active" selected="@(Model.StatusFilter == "Active")">Active</option>
                                    <option value="Inactive" selected="@(Model.StatusFilter == "Inactive")">Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Amenities Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Icon</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Description</th>
                                    <th>Status</th>
                                    <th>Created Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var amenity in Model.Amenities)
                                {
                                    <tr>
                                        <td>
                                            @if (!string.IsNullOrEmpty(amenity.Icon))
                                            {
                                                <i class="@amenity.Icon"></i>
                                            }
                                        </td>
                                        <td>@amenity.AmenityName</td>
                                        <td>
                                            <span class="badge badge-secondary">@amenity.Category</span>
                                        </td>
                                        <td>@amenity.Description</td>
                                        <td>
                                            @if (amenity.IsActive)
                                            {
                                                <span class="badge badge-success">Active</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-danger">Inactive</span>
                                            }
                                        </td>
                                        <td>@amenity.CreatedDate.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="@Url.Action("Details", new { id = amenity.AmenityID })" 
                                                   class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <a href="@Url.Action("Edit", new { id = amenity.AmenityID })" 
                                                   class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                <a href="@Url.Action("AssignToRoom", new { id = amenity.AmenityID })" 
                                                   class="btn btn-sm btn-primary">
                                                    <i class="fas fa-link"></i> Assign
                                                </a>
                                                <button type="button" class="btn btn-sm btn-secondary" 
                                                        onclick="toggleAmenityStatus(@amenity.AmenityID)">
                                                    @if (amenity.IsActive)
                                                    {
                                                        <i class="fas fa-ban"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="fas fa-check"></i>
                                                    }
                                                </button>
                                                <a href="@Url.Action("Delete", new { id = amenity.AmenityID })" 
                                                   class="btn btn-sm btn-danger"
                                                   onclick="return confirm('Are you sure you want to delete this amenity?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if (Model.TotalPages > 1)
                    {
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                @for (int i = 1; i <= Model.TotalPages; i++)
                                {
                                    <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                        <a class="page-link" href="@Url.Action("Index", new { 
                                            page = i, 
                                            searchTerm = Model.SearchTerm, 
                                            categoryFilter = Model.CategoryFilter,
                                            statusFilter = Model.StatusFilter 
                                        })">@i</a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function toggleAmenityStatus(amenityId) {
            $.post('@Url.Action("ToggleStatus")', { id: amenityId }, function(result) {
                if (result.success) {
                    location.reload();
                } else {
                    alert('Error: ' + result.message);
                }
            });
        }
    </script>
}
