using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HotelBooking.Data;
using HotelBooking.Models;
using HotelBooking.Models.ViewModels;
using Microsoft.AspNetCore.Authorization;

namespace HotelBooking.Controllers
{
    [Authorize]
    public class ReservationsController : Controller
    {
        private readonly HotelBookingContext _context;

        public ReservationsController(HotelBookingContext context)
        {
            _context = context;
        }

        // GET: Reservations
        public async Task<IActionResult> Index()
        {
            var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
            
            var reservations = await _context.Reservations
                .Include(r => r.Room)
                    .ThenInclude(rm => rm!.RoomType)
                .Include(r => r.User)
                .Where(r => r.UserID == userId)
                .OrderByDescending(r => r.CreatedDate)
                .ToListAsync();

            return View(reservations);
        }

        // GET: Reservations/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var reservation = await _context.Reservations
                .Include(r => r.Room)
                    .ThenInclude(rm => rm!.RoomType)
                .Include(r => r.User)
                .Include(r => r.Payments)
                .FirstOrDefaultAsync(m => m.ReservationID == id);

            if (reservation == null)
            {
                return NotFound();
            }

            // Check if user owns this reservation
            var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
            if (reservation.UserID != userId)
            {
                return Forbid();
            }

            return View(reservation);
        }

        // GET: Reservations/Cancel/5
        public async Task<IActionResult> Cancel(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var reservation = await _context.Reservations
                .Include(r => r.Room)
                    .ThenInclude(rm => rm!.RoomType)
                .FirstOrDefaultAsync(m => m.ReservationID == id);

            if (reservation == null)
            {
                return NotFound();
            }

            // Check if user owns this reservation
            var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
            if (reservation.UserID != userId)
            {
                return Forbid();
            }

            // Check if reservation can be cancelled (e.g., not within 24 hours of check-in)
            if (reservation.CheckInDate <= DateTime.Today.AddDays(1))
            {
                TempData["Error"] = "Cannot cancel reservation within 24 hours of check-in date.";
                return RedirectToAction("Details", new { id = reservation.ReservationID });
            }

            return View(reservation);
        }

        // POST: Reservations/Cancel/5
        [HttpPost, ActionName("Cancel")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CancelConfirmed(int id)
        {
            var reservation = await _context.Reservations.FindAsync(id);
            
            if (reservation == null)
            {
                return NotFound();
            }

            // Check if user owns this reservation
            var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
            if (reservation.UserID != userId)
            {
                return Forbid();
            }

            // Check if reservation can be cancelled
            if (reservation.CheckInDate <= DateTime.Today.AddDays(1))
            {
                TempData["Error"] = "Cannot cancel reservation within 24 hours of check-in date.";
                return RedirectToAction("Details", new { id = reservation.ReservationID });
            }

            // Create cancellation record
            var cancellation = new Cancellation
            {
                ReservationID = reservation.ReservationID,
                CancellationDate = DateTime.Now,
                Reason = "Cancelled by customer",
                RefundAmount = 0, // Calculate refund based on cancellation policy
                CreatedBy = User.Identity?.Name ?? "System",
                CreatedDate = DateTime.Now
            };

            _context.Cancellations.Add(cancellation);

            // Update reservation status
            reservation.Status = "Cancelled";
            reservation.ModifiedBy = User.Identity?.Name ?? "System";
            reservation.ModifiedDate = DateTime.Now;

            await _context.SaveChangesAsync();

            TempData["Message"] = "Reservation cancelled successfully.";
            return RedirectToAction("Index");
        }

        // GET: Reservations/Modify/5
        public async Task<IActionResult> Modify(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var reservation = await _context.Reservations
                .Include(r => r.Room)
                    .ThenInclude(rm => rm!.RoomType)
                .FirstOrDefaultAsync(m => m.ReservationID == id);

            if (reservation == null)
            {
                return NotFound();
            }

            // Check if user owns this reservation
            var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
            if (reservation.UserID != userId)
            {
                return Forbid();
            }

            // Check if reservation can be modified (e.g., not within 48 hours of check-in)
            if (reservation.CheckInDate <= DateTime.Today.AddDays(2))
            {
                TempData["Error"] = "Cannot modify reservation within 48 hours of check-in date.";
                return RedirectToAction("Details", new { id = reservation.ReservationID });
            }

            var modifyViewModel = new ModifyReservationViewModel
            {
                ReservationID = reservation.ReservationID,
                RoomID = reservation.RoomID,
                Room = reservation.Room,
                CheckInDate = reservation.CheckInDate,
                CheckOutDate = reservation.CheckOutDate,
                NumberOfGuests = reservation.NumberOfGuests,
                OriginalCheckInDate = reservation.CheckInDate,
                OriginalCheckOutDate = reservation.CheckOutDate
            };

            return View(modifyViewModel);
        }

        // POST: Reservations/Modify/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Modify(ModifyReservationViewModel model)
        {
            if (ModelState.IsValid)
            {
                var reservation = await _context.Reservations.FindAsync(model.ReservationID);
                
                if (reservation == null)
                {
                    return NotFound();
                }

                // Check if user owns this reservation
                var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                if (reservation.UserID != userId)
                {
                    return Forbid();
                }

                // Check if new dates are available
                var isAvailable = !await _context.Reservations
                    .AnyAsync(res => res.RoomID == reservation.RoomID &&
                                   res.ReservationID != reservation.ReservationID &&
                                   res.CheckInDate < model.CheckOutDate &&
                                   res.CheckOutDate > model.CheckInDate);

                if (!isAvailable)
                {
                    ModelState.AddModelError("", "The room is not available for the selected dates.");
                    model.Room = await _context.Rooms
                        .Include(r => r.RoomType)
                        .FirstOrDefaultAsync(r => r.RoomID == model.RoomID);
                    return View(model);
                }

                // Update reservation
                reservation.CheckInDate = model.CheckInDate;
                reservation.CheckOutDate = model.CheckOutDate;
                reservation.NumberOfGuests = model.NumberOfGuests;
                reservation.ModifiedBy = User.Identity?.Name ?? "System";
                reservation.ModifiedDate = DateTime.Now;

                await _context.SaveChangesAsync();

                TempData["Message"] = "Reservation modified successfully.";
                return RedirectToAction("Details", new { id = reservation.ReservationID });
            }

            model.Room = await _context.Rooms
                .Include(r => r.RoomType)
                .FirstOrDefaultAsync(r => r.RoomID == model.RoomID);
            return View(model);
        }

        // GET: Reservations/ChangeRoom/5
        public async Task<IActionResult> ChangeRoom(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var reservation = await _context.Reservations
                .Include(r => r.Room)
                    .ThenInclude(rm => rm!.RoomType)
                .FirstOrDefaultAsync(m => m.ReservationID == id);

            if (reservation == null)
            {
                return NotFound();
            }

            // Check if user owns this reservation
            var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
            if (reservation.UserID != userId)
            {
                return Forbid();
            }

            // Check if reservation is active and can be changed
            if (reservation.Status != "Confirmed" || reservation.CheckOutDate <= DateTime.Today)
            {
                TempData["Error"] = "Room change is not available for this reservation.";
                return RedirectToAction("Details", new { id = reservation.ReservationID });
            }

            // Get available rooms for the remaining period
            var changeDate = DateTime.Today > reservation.CheckInDate ? DateTime.Today : reservation.CheckInDate;
            var availableRooms = await GetAvailableRooms(changeDate, reservation.CheckOutDate, reservation.RoomID);

            var viewModel = new RoomChangeViewModel
            {
                ReservationID = reservation.ReservationID,
                CurrentRoomID = reservation.RoomID,
                CurrentRoomNumber = reservation.Room?.RoomNumber,
                CurrentRoomType = reservation.Room?.RoomType?.TypeName,
                CurrentRoomPrice = reservation.Room?.Price ?? 0,
                CheckInDate = reservation.CheckInDate,
                CheckOutDate = reservation.CheckOutDate,
                ChangeDate = changeDate,
                AvailableRooms = availableRooms
            };

            return View(viewModel);
        }

        // POST: Reservations/ChangeRoom
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangeRoom(RoomChangeViewModel model)
        {
            if (ModelState.IsValid)
            {
                var reservation = await _context.Reservations
                    .Include(r => r.Room)
                    .FirstOrDefaultAsync(r => r.ReservationID == model.ReservationID);

                if (reservation == null)
                {
                    return NotFound();
                }

                // Check if user owns this reservation
                var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                if (reservation.UserID != userId)
                {
                    return Forbid();
                }

                // Get new room details
                var newRoom = await _context.Rooms
                    .Include(r => r.RoomType)
                    .FirstOrDefaultAsync(r => r.RoomID == model.NewRoomID);

                if (newRoom == null)
                {
                    ModelState.AddModelError("", "Selected room not found.");
                    model.AvailableRooms = await GetAvailableRooms(model.ChangeDate, model.CheckOutDate, model.CurrentRoomID);
                    return View(model);
                }

                // Calculate price difference
                var remainingNights = (model.CheckOutDate - model.ChangeDate).Days;
                var oldRoomCost = (reservation.Room?.Price ?? 0) * remainingNights;
                var newRoomCost = newRoom.Price * remainingNights;
                var priceDifference = newRoomCost - oldRoomCost;

                // Create room change history record
                var roomChange = new RoomChangeHistory
                {
                    ReservationID = reservation.ReservationID,
                    OldRoomID = reservation.RoomID,
                    NewRoomID = model.NewRoomID,
                    ChangeDate = model.ChangeDate,
                    Reason = model.Reason,
                    OldRoomPrice = reservation.Room?.Price ?? 0,
                    NewRoomPrice = newRoom.Price,
                    PriceDifference = priceDifference,
                    Status = "Approved", // Auto-approve for customers
                    CreatedBy = User.Identity?.Name ?? "Customer",
                    CreatedDate = DateTime.Now,
                    ApprovedBy = User.Identity?.Name ?? "Customer",
                    ApprovedDate = DateTime.Now
                };

                _context.RoomChangeHistories.Add(roomChange);

                // Update reservation
                reservation.RoomID = model.NewRoomID;
                reservation.ModifiedBy = User.Identity?.Name ?? "Customer";
                reservation.ModifiedDate = DateTime.Now;

                await _context.SaveChangesAsync();

                TempData["Message"] = $"Room changed successfully! {(priceDifference > 0 ? $"Additional cost: ${priceDifference:F2}" : priceDifference < 0 ? $"Refund: ${Math.Abs(priceDifference):F2}" : "No price difference")}";
                return RedirectToAction("Details", new { id = reservation.ReservationID });
            }

            model.AvailableRooms = await GetAvailableRooms(model.ChangeDate, model.CheckOutDate, model.CurrentRoomID);
            return View(model);
        }

        // GET: Reservations/AddService/5
        public async Task<IActionResult> AddService(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var reservation = await _context.Reservations
                .Include(r => r.Room)
                .FirstOrDefaultAsync(m => m.ReservationID == id);

            if (reservation == null)
            {
                return NotFound();
            }

            // Check if user owns this reservation
            var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
            if (reservation.UserID != userId)
            {
                return Forbid();
            }

            // Check if reservation is active
            if (reservation.Status != "Confirmed" || reservation.CheckOutDate <= DateTime.Today)
            {
                TempData["Error"] = "Cannot add services to this reservation.";
                return RedirectToAction("Details", new { id = reservation.ReservationID });
            }

            var availableServices = await _context.Services
                .Where(s => s.IsActive)
                .OrderBy(s => s.Category)
                .ThenBy(s => s.ServiceName)
                .ToListAsync();

            var viewModel = new AddServiceViewModel
            {
                ReservationID = reservation.ReservationID,
                RoomNumber = reservation.Room?.RoomNumber,
                CheckInDate = reservation.CheckInDate,
                CheckOutDate = reservation.CheckOutDate,
                AvailableServices = availableServices
            };

            return View(viewModel);
        }

        // POST: Reservations/AddService
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddService(AddServiceViewModel model)
        {
            if (ModelState.IsValid)
            {
                var reservation = await _context.Reservations.FindAsync(model.ReservationID);
                var service = await _context.Services.FindAsync(model.ServiceID);

                if (reservation == null || service == null)
                {
                    return NotFound();
                }

                // Check if user owns this reservation
                var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                if (reservation.UserID != userId)
                {
                    return Forbid();
                }

                var totalPrice = service.UnitPrice * model.Quantity;

                var serviceUsage = new BookingServiceUsage
                {
                    ReservationID = model.ReservationID,
                    ServiceID = model.ServiceID,
                    Quantity = model.Quantity,
                    UnitPrice = service.UnitPrice,
                    TotalPrice = totalPrice,
                    UsageDate = DateTime.Now,
                    Note = model.Note,
                    Status = "Ordered",
                    CreatedBy = User.Identity?.Name ?? "Customer",
                    CreatedDate = DateTime.Now
                };

                _context.BookingServiceUsages.Add(serviceUsage);
                await _context.SaveChangesAsync();

                TempData["Message"] = $"Service '{service.ServiceName}' added successfully! Total cost: ${totalPrice:F2}";
                return RedirectToAction("Details", new { id = reservation.ReservationID });
            }

            model.AvailableServices = await _context.Services
                .Where(s => s.IsActive)
                .OrderBy(s => s.Category)
                .ThenBy(s => s.ServiceName)
                .ToListAsync();

            return View(model);
        }

        // GET: Reservations/Checkout/5
        public async Task<IActionResult> Checkout(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var reservation = await _context.Reservations
                .Include(r => r.Room)
                    .ThenInclude(rm => rm!.RoomType)
                .Include(r => r.Payments)
                .FirstOrDefaultAsync(m => m.ReservationID == id);

            if (reservation == null)
            {
                return NotFound();
            }

            // Check if user owns this reservation
            var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
            if (reservation.UserID != userId)
            {
                return Forbid();
            }

            // Get services used
            var servicesUsed = await _context.BookingServiceUsages
                .Include(s => s.Service)
                .Where(s => s.ReservationID == id)
                .ToListAsync();

            // Get room changes
            var roomChanges = await _context.RoomChangeHistories
                .Include(rc => rc.OldRoom)
                .Include(rc => rc.NewRoom)
                .Where(rc => rc.ReservationID == id)
                .ToListAsync();

            // Calculate costs
            var numberOfNights = (reservation.CheckOutDate - reservation.CheckInDate).Days;
            var roomCost = (reservation.Room?.Price ?? 0) * numberOfNights;
            var serviceCost = servicesUsed.Sum(s => s.TotalPrice);
            var roomChangeCost = roomChanges.Sum(rc => rc.PriceDifference);
            var taxAmount = (roomCost + serviceCost + roomChangeCost) * 0.1m; // 10% tax
            var totalCost = roomCost + serviceCost + roomChangeCost + taxAmount;

            var paidAmount = reservation.Payments?.Sum(p => p.Amount) ?? 0;

            var viewModel = new CheckoutViewModel
            {
                ReservationID = reservation.ReservationID,
                RoomNumber = reservation.Room?.RoomNumber,
                RoomType = reservation.Room?.RoomType?.TypeName,
                CheckInDate = reservation.CheckInDate,
                CheckOutDate = reservation.CheckOutDate,
                NumberOfNights = numberOfNights,
                RoomCost = roomCost + roomChangeCost,
                ServiceCost = serviceCost,
                TaxAmount = taxAmount,
                TotalCost = totalCost,
                ServicesUsed = servicesUsed,
                RoomChanges = roomChanges,
                PaymentStatus = reservation.Status,
                PaidAmount = paidAmount,
                RemainingAmount = totalCost - paidAmount
            };

            return View(viewModel);
        }

        // POST: Reservations/Checkout/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CheckoutConfirmed(int id)
        {
            var reservation = await _context.Reservations.FindAsync(id);

            if (reservation == null)
            {
                return NotFound();
            }

            // Check if user owns this reservation
            var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
            if (reservation.UserID != userId)
            {
                return Forbid();
            }

            // Update reservation status
            reservation.Status = "Checked Out";
            reservation.ModifiedBy = User.Identity?.Name ?? "Customer";
            reservation.ModifiedDate = DateTime.Now;

            await _context.SaveChangesAsync();

            TempData["Message"] = "Checkout completed successfully! Thank you for staying with us.";
            return RedirectToAction("Index");
        }

        // Helper method to get available rooms
        private async Task<List<Room>> GetAvailableRooms(DateTime startDate, DateTime endDate, int excludeRoomId)
        {
            var bookedRoomIds = await _context.Reservations
                .Where(res => res.CheckInDate < endDate && res.CheckOutDate > startDate && res.Status == "Confirmed")
                .Select(res => res.RoomID)
                .ToListAsync();

            return await _context.Rooms
                .Include(r => r.RoomType)
                .Where(r => r.IsActive && r.Status == "Available" &&
                           !bookedRoomIds.Contains(r.RoomID) && r.RoomID != excludeRoomId)
                .OrderBy(r => r.RoomType!.TypeName)
                .ThenBy(r => r.Price)
                .ToListAsync();
        }
    }
}
