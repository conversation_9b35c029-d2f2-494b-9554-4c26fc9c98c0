@{
    ViewData["Title"] = "Notifications";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-bell me-2"></i>My Notifications
                    </h4>
                </div>
                <div class="card-body">
                    @if (ViewBag.Message != null)
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>@ViewBag.Message
                        </div>
                    }

                    <!-- Sample notifications for demo -->
                    <div class="notification-list">
                        <div class="notification-item border-bottom pb-3 mb-3">
                            <div class="d-flex align-items-start">
                                <div class="notification-icon me-3">
                                    <i class="fas fa-check-circle text-success fa-2x"></i>
                                </div>
                                <div class="notification-content flex-grow-1">
                                    <h6 class="notification-title mb-1">Booking Confirmed</h6>
                                    <p class="notification-text mb-1">Your booking for Room 101 has been confirmed for January 15-17, 2025.</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>2 hours ago
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="notification-item border-bottom pb-3 mb-3">
                            <div class="d-flex align-items-start">
                                <div class="notification-icon me-3">
                                    <i class="fas fa-calendar-alt text-primary fa-2x"></i>
                                </div>
                                <div class="notification-content flex-grow-1">
                                    <h6 class="notification-title mb-1">Check-in Reminder</h6>
                                    <p class="notification-text mb-1">Don't forget! Your check-in is tomorrow at 3:00 PM.</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>1 day ago
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="notification-item border-bottom pb-3 mb-3">
                            <div class="d-flex align-items-start">
                                <div class="notification-icon me-3">
                                    <i class="fas fa-star text-warning fa-2x"></i>
                                </div>
                                <div class="notification-content flex-grow-1">
                                    <h6 class="notification-title mb-1">Rate Your Stay</h6>
                                    <p class="notification-text mb-1">How was your recent stay? We'd love to hear your feedback!</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>3 days ago
                                    </small>
                                    <div class="mt-2">
                                        <a href="@Url.Action("Feedback", "Customer")" class="btn btn-sm btn-outline-warning">
                                            <i class="fas fa-comment-dots me-1"></i>Give Feedback
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="notification-item">
                            <div class="d-flex align-items-start">
                                <div class="notification-icon me-3">
                                    <i class="fas fa-gift text-danger fa-2x"></i>
                                </div>
                                <div class="notification-content flex-grow-1">
                                    <h6 class="notification-title mb-1">Special Offer</h6>
                                    <p class="notification-text mb-1">Get 20% off your next booking! Use code WELCOME20.</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>1 week ago
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <p class="text-muted">
                            <i class="fas fa-info-circle me-2"></i>
                            Real-time notifications will be implemented in future updates.
                        </p>
                        <a href="@Url.Action("Index", "Reservations")" class="btn btn-primary">
                            <i class="fas fa-bookmark me-2"></i>View My Bookings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .notification-item {
        transition: background-color 0.2s;
    }

    .notification-item:hover {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 10px;
        margin: -10px;
    }

    .notification-title {
        font-weight: 600;
        color: #333;
    }

    .notification-text {
        color: #666;
        margin-bottom: 0;
    }

    .notification-icon {
        min-width: 50px;
    }
</style>
