@model HotelBooking.Models.ViewModels.UserListViewModel
@{
    ViewData["Title"] = "User Management";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users"></i> User Management
                    </h3>
                    <div class="card-tools">
                        <a href="@Url.Action("Create")" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New User
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" name="searchTerm" value="@Model.SearchTerm" 
                                       class="form-control" placeholder="Search users...">
                            </div>
                            <div class="col-md-2">
                                <select name="roleFilter" class="form-control">
                                    <option value="">All Roles</option>
                                    @foreach (var role in Model.Roles)
                                    {
                                        <option value="@role.Id" selected="@(Model.RoleFilter == role.Id.ToString())">
                                            @role.RoleName
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="statusFilter" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="Active" selected="@(Model.StatusFilter == "Active")">Active</option>
                                    <option value="Inactive" selected="@(Model.StatusFilter == "Inactive")">Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Users Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Created Date</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in Model.Users)
                                {
                                    <tr>
                                        <td>@user.UserName</td>
                                        <td>@user.Email</td>
                                        <td>@user.PhoneNumber</td>
                                        <td>
                                            <span class="badge badge-info">@user.RoleName</span>
                                        </td>
                                        <td>
                                            @if (user.IsActive)
                                            {
                                                <span class="badge badge-success">Active</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-danger">Inactive</span>
                                            }
                                        </td>
                                        <td>@user.CreatedDate.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            @if (user.LastLogin.HasValue)
                                            {
                                                @user.LastLogin.Value.ToString("dd/MM/yyyy HH:mm")
                                            }
                                            else
                                            {
                                                <span class="text-muted">Never</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="@Url.Action("Details", new { id = user.UserId })" 
                                                   class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <a href="@Url.Action("Edit", new { id = user.UserId })" 
                                                   class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                <button type="button" class="btn btn-sm btn-secondary" 
                                                        onclick="toggleUserStatus(@user.UserId)">
                                                    @if (user.IsActive)
                                                    {
                                                        <i class="fas fa-ban"></i> @("Deactivate")
                                                    }
                                                    else
                                                    {
                                                        <i class="fas fa-check"></i> @("Activate")
                                                    }
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if (Model.TotalPages > 1)
                    {
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                @for (int i = 1; i <= Model.TotalPages; i++)
                                {
                                    <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                        <a class="page-link" href="@Url.Action("Index", new { 
                                            page = i, 
                                            searchTerm = Model.SearchTerm, 
                                            roleFilter = Model.RoleFilter,
                                            statusFilter = Model.StatusFilter 
                                        })">@i</a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function toggleUserStatus(userId) {
            if (confirm('Are you sure you want to change this user\'s status?')) {
                $.post('@Url.Action("ToggleStatus")', { id: userId }, function(result) {
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + result.message);
                    }
                });
            }
        }
    </script>
}
