@model HotelBooking.Models.ViewModels.AmenityViewModel
@{
    ViewData["Title"] = "Create Amenity";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-star"></i> Create New Amenity
                    </h3>
                </div>

                <form asp-action="Create" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="form-group">
                            <label asp-for="AmenityName" class="control-label"></label>
                            <input asp-for="AmenityName" class="form-control" />
                            <span asp-validation-for="AmenityName" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="Description" class="control-label"></label>
                            <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="Category" class="control-label"></label>
                            <select asp-for="Category" class="form-control">
                                <option value="">Select Category</option>
                                @foreach (var category in Model.Categories)
                                {
                                    <option value="@category">@category</option>
                                }
                            </select>
                            <span asp-validation-for="Category" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="Icon" class="control-label"></label>
                            <input asp-for="Icon" class="form-control" placeholder="e.g., fas fa-wifi" />
                            <span asp-validation-for="Icon" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Use FontAwesome icon classes (e.g., fas fa-wifi, fas fa-swimming-pool)
                            </small>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                <label asp-for="IsActive" class="form-check-label"></label>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create Amenity
                        </button>
                        <a href="@Url.Action("Index")" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
