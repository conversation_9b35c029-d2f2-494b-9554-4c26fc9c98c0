@model IEnumerable<HotelBooking.Models.Room>
@{
    ViewData["Title"] = "Search Results";
    var checkIn = ViewBag.CheckIn as string;
    var checkOut = ViewBag.CheckOut as string;
    var guests = ViewBag.Guests as int?;
    var roomTypeId = ViewBag.RoomTypeId as int?;
    var maxPrice = ViewBag.MaxPrice as decimal?;
}

<div class="container">
    <!-- Search Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-2">Search Results</h4>
                            <div class="search-summary">
                                <span class="me-3">
                                    <i class="fas fa-calendar-alt text-primary me-1"></i>
                                    @if (!string.IsNullOrEmpty(checkIn) && !string.IsNullOrEmpty(checkOut))
                                    {
                                        <text>@DateTime.Parse(checkIn).ToString("MMM dd") - @DateTime.Parse(checkOut).ToString("MMM dd, yyyy")</text>
                                    }
                                </span>
                                <span class="me-3">
                                    <i class="fas fa-users text-primary me-1"></i>
                                    @(guests ?? 0) guest@(guests > 1 ? "s" : "")
                                </span>
                                @if (maxPrice.HasValue)
                                {
                                    <span class="me-3">
                                        <i class="fas fa-dollar-sign text-primary me-1"></i>
                                        Under $@maxPrice
                                    </span>
                                }
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a asp-action="Search" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-1"></i>Modify Search
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results -->
    <div class="row mb-4">
        <div class="col-12">
            <h5>@Model.Count() room@(Model.Count() != 1 ? "s" : "") found</h5>
        </div>
    </div>

    @if (Model.Any())
    {
        <div class="row">
            @foreach (var room in Model)
            {
                <div class="col-lg-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="card-title mb-0">Room @room.RoomNumber</h5>
                                        <span class="badge bg-success">Available</span>
                                    </div>

                                    <h6 class="card-subtitle mb-3 text-primary">@room.RoomType?.TypeName</h6>

                                    <div class="room-features mb-3">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">Bed Type</small>
                                                <div><strong>@room.BedType</strong></div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">View</small>
                                                <div><strong>@room.ViewType</strong></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-2">
                                        <small class="text-muted">Max Occupancy</small>
                                        <div><i class="fas fa-users text-primary me-1"></i><strong>@room.RoomType?.MaxOccupancy guests</strong></div>
                                    </div>
                                </div>

                                <div class="col-md-4 text-center">
                                    <div class="price-display mb-3">
                                        <div class="h4 text-primary fw-bold mb-0">$@room.Price</div>
                                        <small class="text-muted">per night</small>
                                        @if (!string.IsNullOrEmpty(checkIn) && !string.IsNullOrEmpty(checkOut))
                                        {
                                            var nights = (DateTime.Parse(checkOut) - DateTime.Parse(checkIn)).Days;
                                            var totalPrice = room.Price * nights;
                                            <div class="mt-2">
                                                <small class="text-muted">@nights night@(nights > 1 ? "s" : "") total:</small>
                                                <div class="fw-bold text-success">$@totalPrice</div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer bg-transparent">
                            <div class="d-grid gap-2 d-md-flex">
                                <a asp-action="Details" asp-route-id="@room.RoomID" class="btn btn-outline-primary flex-fill">
                                    View Details
                                </a>
                                <a asp-action="Book" asp-route-id="@room.RoomID"
                                   asp-route-checkIn="@checkIn" asp-route-checkOut="@checkOut" asp-route-guests="@guests"
                                   class="btn btn-primary flex-fill">
                                    Book Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-search fa-3x text-muted"></i>
            </div>
            <h3 class="text-muted">No rooms found</h3>
            <p class="text-muted mb-4">
                Sorry, no rooms match your search criteria. Try adjusting your search parameters.
            </p>
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <a asp-action="Search" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>Search Again
                </a>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    View All Rooms
                </a>
            </div>
        </div>
    }

    <!-- Search Tips for No Results -->
    @if (!Model.Any())
    {
        <div class="row mt-4">
            <div class="col-lg-8 mx-auto">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-lightbulb text-warning me-2"></i>Search Suggestions
                        </h6>
                        <ul class="mb-0 small">
                            <li>Try different check-in or check-out dates</li>
                            <li>Consider reducing the number of guests</li>
                            <li>Remove room type or price filters</li>
                            <li>Contact us directly for special accommodations</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
.price-display {
    border-left: 3px solid var(--bs-primary);
    padding-left: 1rem;
}

.room-features {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.9rem;
}

.search-summary {
    font-size: 0.95rem;
}
</style>
