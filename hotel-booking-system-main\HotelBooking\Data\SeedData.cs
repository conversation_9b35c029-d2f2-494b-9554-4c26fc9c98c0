using Microsoft.AspNetCore.Identity;
using HotelBooking.Models;
using Microsoft.EntityFrameworkCore;

namespace HotelBooking.Data
{
    public static class SeedData
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            try
            {
                using var context = new HotelBookingContext(
                    serviceProvider.GetRequiredService<DbContextOptions<HotelBookingContext>>());

                var roleManager = serviceProvider.GetRequiredService<RoleManager<CustomRole>>();
                var userManager = serviceProvider.GetRequiredService<UserManager<CustomUser>>();

                // Create roles if they don't exist
                string[] roleNames = { "Admin", "Customer", "Staff" };
                foreach (var roleName in roleNames)
                {
                    var roleExist = await roleManager.RoleExistsAsync(roleName);
                    if (!roleExist)
                    {
                        var role = new CustomRole
                        {
                            Name = roleName,
                            NormalizedName = roleName.ToUpper(),
                            RoleName = roleName,
                            Description = $"{roleName} role",
                            IsActive = true,
                            CreatedBy = "System",
                            CreatedDate = DateTime.Now
                        };
                        await roleManager.CreateAsync(role);
                    }
                }

                // Create simple test users
                var testUsers = new[]
                {
                    new { Email = "<EMAIL>", UserName = "admin", Role = "Admin", Password = "123456" },
                    new { Email = "<EMAIL>", UserName = "staff", Role = "Staff", Password = "123456" },
                    new { Email = "<EMAIL>", UserName = "customer", Role = "Customer", Password = "123456" }
                };

                foreach (var userData in testUsers)
                {
                    var user = await userManager.FindByEmailAsync(userData.Email);
                    if (user == null)
                    {
                        user = new CustomUser
                        {
                            UserName = userData.UserName,
                            Email = userData.Email,
                            EmailConfirmed = true,
                            PhoneNumber = "0123456789",
                            IsActive = true,
                            CreatedBy = "System",
                            CreatedDate = DateTime.Now
                        };

                        var result = await userManager.CreateAsync(user, userData.Password);
                        if (result.Succeeded)
                        {
                            await userManager.AddToRoleAsync(user, userData.Role);
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking app startup
                Console.WriteLine($"Error seeding data: {ex.Message}");
            }
        }
    }
}
