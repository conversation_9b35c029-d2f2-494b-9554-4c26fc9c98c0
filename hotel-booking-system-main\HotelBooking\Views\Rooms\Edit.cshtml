@model HotelBooking.Models.ViewModels.RoomViewModel
@{
    ViewData["Title"] = "Edit Room";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bed"></i> Edit Room @Model.RoomNumber
                    </h3>
                </div>

                <form asp-action="Edit" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <input type="hidden" asp-for="RoomID" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="RoomNumber" class="control-label"></label>
                                    <input asp-for="RoomNumber" class="form-control" />
                                    <span asp-validation-for="RoomNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="RoomTypeID" class="control-label"></label>
                                    <select asp-for="RoomTypeID" class="form-control">
                                        <option value="">Select Room Type</option>
                                        @foreach (var roomType in Model.RoomTypes)
                                        {
                                            <option value="@roomType.RoomTypeID" selected="@(Model.RoomTypeID == roomType.RoomTypeID)">
                                                @roomType.TypeName - Max @roomType.MaxOccupancy guests
                                            </option>
                                        }
                                    </select>
                                    <span asp-validation-for="RoomTypeID" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Price" class="control-label"></label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input asp-for="Price" class="form-control" type="number" step="0.01" />
                                    </div>
                                    <span asp-validation-for="Price" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Status" class="control-label"></label>
                                    <select asp-for="Status" class="form-control">
                                        @foreach (var status in Model.StatusOptions)
                                        {
                                            <option value="@status" selected="@(Model.Status == status)">@status</option>
                                        }
                                    </select>
                                    <span asp-validation-for="Status" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="Description" class="control-label"></label>
                            <textarea asp-for="Description" class="form-control" rows="4" 
                                      placeholder="Enter room description, special features, etc."></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label class="control-label">Amenities</label>
                            <div class="card card-outline card-info">
                                <div class="card-header">
                                    <h6 class="card-title">Select amenities available in this room</h6>
                                    <div class="card-tools">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllAmenities()">
                                            Select All
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllAmenities()">
                                            Deselect All
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    @if (Model.Amenities.Any())
                                    {
                                        <div class="row">
                                            @foreach (var amenity in Model.Amenities.OrderBy(a => a.Category).ThenBy(a => a.AmenityName))
                                            {
                                                var isSelected = Model.SelectedAmenityIds.Contains(amenity.AmenityID);
                                                <div class="col-md-4 col-lg-3 mb-2">
                                                    <div class="form-check">
                                                        <input type="checkbox" 
                                                               name="SelectedAmenityIds" 
                                                               value="@amenity.AmenityID" 
                                                               class="form-check-input amenity-checkbox" 
                                                               id="<EMAIL>"
                                                               @(isSelected ? "checked" : "") />
                                                        <label class="form-check-label" for="<EMAIL>">
                                                            @if (!string.IsNullOrEmpty(amenity.Icon))
                                                            {
                                                                <i class="@amenity.Icon"></i>
                                                            }
                                                            @amenity.AmenityName
                                                            <small class="text-muted d-block">@amenity.Category</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="alert alert-info mb-0">
                                            <i class="fas fa-info-circle"></i>
                                            No amenities available. Please create some amenities first.
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                <label asp-for="IsActive" class="form-check-label"></label>
                                <small class="form-text text-muted">
                                    Inactive rooms will not be available for booking
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Room
                        </button>
                        <a href="@Url.Action("Details", new { id = Model.RoomID })" class="btn btn-info">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        <a href="@Url.Action("Index")" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        function selectAllAmenities() {
            $('.amenity-checkbox').prop('checked', true);
        }

        function deselectAllAmenities() {
            $('.amenity-checkbox').prop('checked', false);
        }

        // Update price display
        $('#Price').on('input', function() {
            const value = parseFloat($(this).val());
            if (!isNaN(value)) {
                $(this).val(value.toFixed(2));
            }
        });
    </script>
}
