@model IEnumerable<HotelBooking.Models.Room>
@{
    ViewData["Title"] = "All Rooms";
}

<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-5">All Rooms</h1>
            <p class="lead">Browse our available rooms and find your perfect stay</p>
        </div>
        <div class="col-md-4 text-end">
            @if (User.IsInRole("Admin") || User.IsInRole("Staff"))
            {
                <a asp-action="Create" class="btn btn-success btn-lg me-2">
                    <i class="fas fa-plus"></i> Add Room
                </a>
            }
            <a asp-action="Search" class="btn btn-primary btn-lg">
                <i class="fas fa-search"></i> Advanced Search
            </a>
        </div>
    </div>

    <div class="row">
        @foreach (var room in Model)
        {
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shadow-sm room-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title mb-0">Room @room.RoomNumber</h5>
                            <span class="badge bg-success">@room.Status</span>
                        </div>
                        
                        <h6 class="card-subtitle mb-3 text-primary">@room.RoomType?.TypeName</h6>
                        
                        <div class="room-details mb-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted d-block">Bed Type</small>
                                    <strong>@room.BedType</strong>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted d-block">View</small>
                                    <strong>@room.ViewType</strong>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">Max Occupancy</small>
                            <div><strong>@room.RoomType?.MaxOccupancy guests</strong></div>
                        </div>

                        <div class="price-section mb-3">
                            <span class="h4 text-primary fw-bold">$@room.Price</span>
                            <small class="text-muted">per night</small>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <div class="d-grid gap-2">
                            <a asp-action="Details" asp-route-id="@room.RoomID" class="btn btn-outline-primary">
                                View Details
                            </a>
                            @if (User.IsInRole("Admin") || User.IsInRole("Staff"))
                            {
                                <div class="btn-group" role="group">
                                    <a asp-action="Edit" asp-route-id="@room.RoomID" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <a asp-action="Delete" asp-route-id="@room.RoomID" class="btn btn-danger btn-sm"
                                           onclick="return confirm('Are you sure you want to delete this room?')">
                                            <i class="fas fa-trash"></i> Delete
                                        </a>
                                    }
                                </div>
                            }
                            @if (room.Status == "Available")
                            {
                                <a asp-action="Book" asp-route-id="@room.RoomID" class="btn btn-primary">
                                    Book Now
                                </a>
                            }
                            else
                            {
                                <button class="btn btn-secondary" disabled>Not Available</button>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    @if (!Model.Any())
    {
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-bed fa-3x text-muted"></i>
            </div>
            <h3 class="text-muted">No rooms available</h3>
            <p class="text-muted">Please check back later or contact us for assistance.</p>
            <a asp-controller="Home" asp-action="Index" class="btn btn-primary">Back to Home</a>
        </div>
    }
</div>

<style>
.room-card {
    transition: transform 0.2s ease-in-out;
}

.room-card:hover {
    transform: translateY(-5px);
}

.price-section {
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
}

.room-details {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
}
</style>
